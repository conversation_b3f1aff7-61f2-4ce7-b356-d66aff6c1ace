import 'package:flutter/material.dart';

class UsuarioInfo extends StatelessWidget {
  final Map<String, dynamic> usuario;

  const UsuarioInfo({super.key, required this.usuario});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            usuario['full_name'] ?? usuario['username'] ?? 'Perfil de Usuario'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                CircleAvatar(
                  radius: 50,
                  backgroundImage: usuario['avatar_url'] != null
                      ? NetworkImage(usuario['avatar_url'])
                      : null,
                  child: usuario['avatar_url'] == null
                      ? const Icon(Icons.person, size: 50)
                      : null,
                ),
                const SizedBox(height: 20),
                InfoRow(
                  title: 'Nombre Completo',
                  content: usuario['full_name'] ?? 'No especificado',
                ),
                const Divider(),
                InfoRow(
                  title: 'Nombre de Usuario',
                  content: usuario['username'] ?? 'No especificado',
                ),
                const Divider(),
                InfoRow(
                  title: 'Email',
                  content: usuario['email'] ?? 'No especificado',
                ),
                const Divider(),
                InfoRow(
                  title: 'Rol',
                  content: usuario['roles']['role_name'] ?? 'No especificado',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class InfoRow extends StatelessWidget {
  final String title;
  final String content;

  const InfoRow({
    super.key,
    required this.title,
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            content,
            style: const TextStyle(fontSize: 18),
          ),
        ],
      ),
    );
  }
}
