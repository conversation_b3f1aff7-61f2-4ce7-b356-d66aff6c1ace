import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class CuadroViewer extends StatefulWidget {
  final String cuadroId;

  const CuadroViewer({
    super.key,
    required this.cuadroId,
  });

  @override
  State<CuadroViewer> createState() => _CuadroViewerState();
}

class _CuadroViewerState extends State<CuadroViewer> {
  Map<String, dynamic>? cuadroData;
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    _cargarCuadro();
  }

  Future<void> _cargarCuadro() async {
    try {
      final response = await Supabase.instance.client
          .from('cuadros')
          .select('titulo, contenido, created_at, updated_at')
          .eq('id', int.parse(widget.cuadroId))
          .maybeSingle();

      if (response != null) {
        setState(() {
          cuadroData = response;
          isLoading = false;
        });
      } else {
        setState(() {
          error = 'Cuadro no encontrado';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        error = 'Error cargando cuadro: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(cuadroData?['titulo'] ?? 'Cuadro ${widget.cuadroId}'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error, size: 64, color: Colors.red),
                      SizedBox(height: 16),
                      Text(error!, style: TextStyle(fontSize: 18)),
                      SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text('Volver'),
                      ),
                    ],
                  ),
                )
              : _buildCuadroContent(),
    );
  }

  Widget _buildCuadroContent() {
    if (cuadroData == null) return Container();

    final titulo = cuadroData!['titulo'] ?? '';
    final contenidoData = cuadroData!['contenido'] as Map<String, dynamic>?;

    if (contenidoData == null) {
      return const Center(child: Text('No hay contenido para mostrar'));
    }

    final columnas = contenidoData['columnas'] ?? 1;
    final filas = contenidoData['filas'] ?? 1;
    final colorValue = contenidoData['color'] as int?;
    final colorCuadro =
        colorValue != null ? Color(colorValue) : Colors.grey[600]!;
    final matrizData = contenidoData['matriz'] as List<dynamic>? ?? [];

    // Crear matriz de celdas con la nueva estructura
    List<List<Map<String, dynamic>>> matriz = List.generate(
      filas,
      (fila) => List.generate(
        columnas,
        (columna) => {'contenido': '', 'esVineta': false},
      ),
    );

    // Llenar la matriz con los datos
    for (int fila = 0; fila < filas && fila < matrizData.length; fila++) {
      final filaData = matrizData[fila] as List<dynamic>;
      for (int columna = 0;
          columna < columnas && columna < filaData.length;
          columna++) {
        final celdaData = filaData[columna] as Map<String, dynamic>;
        matriz[fila][columna] = {
          'contenido': celdaData['contenido'] ?? '',
          'esVineta': celdaData['esVineta'] ?? false,
        };
      }
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Información del cuadro
          Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Información del Cuadro',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('ID: ${widget.cuadroId}'),
                  Text('Título: $titulo'),
                  Text('Dimensiones: ${columnas}x$filas'),
                  Text('Creado: ${cuadroData!['created_at']}'),
                  if (cuadroData!['updated_at'] != null)
                    Text('Actualizado: ${cuadroData!['updated_at']}'),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // Cuadro renderizado
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: colorCuadro, width: 2.0),
              borderRadius: BorderRadius.circular(12.0),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Título del cuadro
                if (titulo.isNotEmpty)
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: Colors.teal[50],
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10.0),
                        topRight: Radius.circular(10.0),
                      ),
                    ),
                    child: Text(
                      titulo,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal[800],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                // Tabla del cuadro
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Table(
                    border: TableBorder(
                      horizontalInside: BorderSide(
                          color: colorCuadro.withValues(alpha: 0.5),
                          width: 1.0),
                      verticalInside: BorderSide(
                          color: colorCuadro.withValues(alpha: 0.5),
                          width: 1.0),
                    ),
                    defaultColumnWidth: IntrinsicColumnWidth(),
                    children: matriz.map((fila) {
                      return TableRow(
                        children: fila.map((celda) {
                          final elementos =
                              celda['elementos'] as List<dynamic>? ?? [];

                          // Obtener color de la celda individual si existe
                          Color colorCelda = colorCuadro;
                          if (celda['colorFondo'] != null) {
                            colorCelda = Color(celda['colorFondo'] as int);
                          }

                          return Container(
                            padding: EdgeInsets.all(16.0),
                            decoration: BoxDecoration(
                              color: colorCelda.withValues(
                                  alpha: 0.1), // Fondo del cuadro individual
                              border: Border.all(color: colorCelda, width: 1),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ...elementos.map((elemento) {
                                  final elementData =
                                      elemento as Map<String, dynamic>;
                                  final texto =
                                      elementData['texto'] as String? ?? '';
                                  final tipo =
                                      elementData['tipo'] as String? ?? 'texto';
                                  final esVineta = tipo == 'listaDesordenada';

                                  if (texto.isEmpty) return SizedBox.shrink();

                                  return Padding(
                                    padding: EdgeInsets.only(bottom: 4.0),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        if (esVineta) ...[
                                          Text(
                                            '•',
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: Colors.black87,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          SizedBox(width: 8.0),
                                        ],
                                        Expanded(
                                          child: Text(
                                            texto,
                                            style: TextStyle(
                                              fontSize: 15,
                                              color: Colors.black87,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }),
                              ],
                            ),
                          );
                        }).toList(),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
