import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class CuadroViewer extends StatefulWidget {
  final String cuadroId;

  const CuadroViewer({
    super.key,
    required this.cuadroId,
  });

  @override
  State<CuadroViewer> createState() => _CuadroViewerState();
}

class _CuadroViewerState extends State<CuadroViewer> {
  Map<String, dynamic>? cuadroData;
  bool isLoading = true;
  String? error;
  List<double> _anchosColumnas = [];
  Color _colorCuadroGlobal = Colors.grey[600]!;

  @override
  void initState() {
    super.initState();
    _cargarCuadro();
  }

  Future<void> _cargarCuadro() async {
    try {
      // Convertir cuadroId a int si es necesario
      final cuadroIdInt =
          int.tryParse(widget.cuadroId) ?? int.parse(widget.cuadroId);

      final response = await Supabase.instance.client
          .from('cuadros')
          .select('titulo, json_data, created_at, updated_at')
          .eq('cuadro_id', cuadroIdInt)
          .maybeSingle();

      if (response != null) {
        // Procesar json_data para extraer anchos de columnas y color
        if (response['json_data'] != null) {
          Map<String, dynamic> contenidoData;
          try {
            contenidoData = response['json_data'] is String
                ? jsonDecode(response['json_data'] as String)
                    as Map<String, dynamic>
                : response['json_data'] as Map<String, dynamic>;
          } catch (e) {
            print('Error parseando JSON: $e');
            contenidoData = {};
          }

          // Cargar anchos de columnas
          if (contenidoData['anchosColumnas'] != null) {
            _anchosColumnas =
                List<double>.from(contenidoData['anchosColumnas']);
          }

          // Cargar color si existe
          if (contenidoData['color'] != null) {
            _colorCuadroGlobal = Color(contenidoData['color'] as int);
          }
        }

        setState(() {
          cuadroData = response;
          isLoading = false;
        });
      } else {
        setState(() {
          error = 'Cuadro no encontrado';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        error = 'Error cargando cuadro: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(cuadroData?['titulo'] ?? 'Cuadro ${widget.cuadroId}'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error, size: 64, color: Colors.red),
                      SizedBox(height: 16),
                      Text(error!, style: TextStyle(fontSize: 18)),
                      SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text('Volver'),
                      ),
                    ],
                  ),
                )
              : _buildCuadroContent(),
    );
  }

  Widget _buildCuadroContent() {
    if (cuadroData == null) return Container();

    final titulo = cuadroData!['titulo'] ?? '';
    final contenidoData = cuadroData!['json_data'] as Map<String, dynamic>?;

    print('DEBUG - Título: $titulo');
    print('DEBUG - Contenido Data: $contenidoData');

    if (contenidoData == null) {
      return const Center(child: Text('No hay contenido para mostrar'));
    }

    final columnas = contenidoData['columnas'] ?? 1;
    final filas = contenidoData['filas'] ?? 1;
    final colorValue = contenidoData['color'] as int?;
    final colorCuadro =
        colorValue != null ? Color(colorValue) : Colors.grey[600]!;

    print('DEBUG - Filas: $filas, Columnas: $columnas');
    print('DEBUG - Contenido Data keys: ${contenidoData.keys}');

    // Crear matriz de celdas
    List<List<Map<String, dynamic>>> matriz = List.generate(
      filas,
      (fila) => List.generate(
        columnas,
        (columna) =>
            {'titulo': '', 'contenido': [], 'colorFondo': colorCuadro.value},
      ),
    );

    // Procesar celdas del formato original
    if (contenidoData['celdas'] != null) {
      final celdasData = contenidoData['celdas'] as List<dynamic>;
      print('DEBUG - Procesando ${celdasData.length} celdas');

      for (var celdaInfo in celdasData) {
        final celdaData = celdaInfo as Map<String, dynamic>;
        final fila = celdaData['fila'] as int;
        final columna = celdaData['columna'] as int;
        final titulo = celdaData['titulo'] as String? ?? '';
        final contenido = celdaData['contenido'] as List<dynamic>? ?? [];

        if (fila < filas && columna < columnas) {
          matriz[fila][columna] = {
            'titulo': titulo,
            'contenido': contenido,
            'colorFondo': colorCuadro.value,
          };
        }
      }
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Información del cuadro
          Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Información del Cuadro',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('ID: ${widget.cuadroId}'),
                  Text('Título: $titulo'),
                  Text('Dimensiones: ${columnas}x$filas'),
                  Text('Creado: ${cuadroData!['created_at']}'),
                  if (cuadroData!['updated_at'] != null)
                    Text('Actualizado: ${cuadroData!['updated_at']}'),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // Cuadro renderizado
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: colorCuadro, width: 2.0),
              borderRadius: BorderRadius.circular(12.0),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Título del cuadro
                if (titulo.isNotEmpty)
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: Colors.teal[50],
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10.0),
                        topRight: Radius.circular(10.0),
                      ),
                    ),
                    child: Text(
                      titulo,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal[800],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                // Tabla del cuadro
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Column(
                    children: matriz.map((fila) {
                      return IntrinsicHeight(
                        child: Row(
                          children: fila.asMap().entries.map((entry) {
                            final columna = entry.key;
                            final celda = entry.value;
                            final titulo = celda['titulo'] as String? ?? '';
                            final contenido =
                                celda['contenido'] as List<dynamic>? ?? [];

                            // Obtener color de la celda individual si existe
                            Color colorCelda = colorCuadro;
                            if (celda['colorFondo'] != null) {
                              colorCelda = Color(celda['colorFondo'] as int);
                            }

                            // Obtener ancho de columna
                            double anchoColumna = 200.0; // Ancho por defecto
                            if (_anchosColumnas.isNotEmpty &&
                                columna < _anchosColumnas.length) {
                              anchoColumna = _anchosColumnas[columna];
                            }

                            return Container(
                              width: anchoColumna,
                              padding: EdgeInsets.all(16.0),
                              decoration: BoxDecoration(
                                color: colorCelda.withValues(
                                    alpha: 0.1), // Fondo del cuadro individual
                                border: Border.all(color: colorCelda, width: 1),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  // Mostrar título si existe
                                  if (titulo.isNotEmpty)
                                    Padding(
                                      padding: EdgeInsets.only(bottom: 8.0),
                                      child: Text(
                                        titulo,
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    ),
                                  // Mostrar contenido como viñetas
                                  ...contenido.map((item) {
                                    final texto = item.toString();
                                    if (texto.isEmpty) return SizedBox.shrink();

                                    return Padding(
                                      padding: EdgeInsets.only(bottom: 4.0),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '•',
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: Colors.black87,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          SizedBox(width: 8.0),
                                          Expanded(
                                            child: Text(
                                              texto,
                                              style: TextStyle(
                                                fontSize: 15,
                                                color: Colors.black87,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  }),
                                ],
                              ),
                            );
                          }).toList(),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
