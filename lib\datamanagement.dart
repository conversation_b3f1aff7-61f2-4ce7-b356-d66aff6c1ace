import 'package:flutter/material.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:dio/dio.dart' as dio;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:logging/logging.dart';
import 'dart:async';
import 'package:path/path.dart' as path;
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:convert/convert.dart';
import 'database_helper.dart';

class Datamanagement extends StatefulWidget {
  const Datamanagement({super.key});

  @override
  DatamanagementState createState() => DatamanagementState();
}

class DatamanagementState extends State<Datamanagement> {
  List<FileSystemEntity> archivos = [];
  double _zoomFactor = 1.0;
  bool mostrarAlbumes = true;
  bool isLoading = true;
  Directory? galeriaDir;
  Future<void>? _uploadFuture;
  double _uploadProgress = 0.0;
  double _uploadSpeed = 0.0; // Velocidad de subida en KB/s
  int _lastSent = 0;
  DateTime? _lastTime;
  int _totalSize = 0; // Tamaño total del archivo en bytes
  int _sentSize = 0; // Tamaño enviado del archivo en bytes
  bool _isDownloading = false;
  final dbHelper = DatabaseHelper();
  final Logger _logger = Logger('DatamanagementLogger');

  @override
  void initState() {
    super.initState();
    _cargarArchivos();
  }

  Future<void> _cargarArchivos() async {
    setState(() {
      isLoading = true;
    });

    try {
      final Directory directory =
          Directory('C:/FlutterProjects/appmanager/Galeria medicapp');
      final List<FileSystemEntity> archivos =
          await compute(_listarArchivosYDirectorios, directory);

      if (!mounted) return;

      // Ordenar archivos: directorios primero, archivos después
      archivos.sort((a, b) {
        if (a is Directory && b is! Directory) {
          return -1;
        } else if (a is! Directory && b is Directory) {
          return 1;
        } else {
          return 0;
        }
      });

      setState(() {
        galeriaDir = directory;
        this.archivos = archivos;
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Error al cargar archivos: $e');
      if (!mounted) return;
      setState(() {
        isLoading = false;
      });
    }
  }

  static List<FileSystemEntity> _listarArchivosYDirectorios(
      Directory directory) {
    return directory.listSync();
  }

  Future<void> _downloadJsonFile() async {
    try {
      final jsonData = await dbHelper.fetchAllDataFromSupabase();
      await _saveJsonToFile(jsonData);
      await _cargarArchivos(); // Recargar la lista de archivos después de la descarga
    } catch (e) {
      _logger.severe('Error downloading JSON file: $e');
    }
  }

  Future<void> _saveJsonToFile(Map<String, dynamic> jsonData) async {
    try {
      final directory =
          Directory('C:\\FlutterProjects\\appmanager\\Galeria medicapp');
      if (!directory.existsSync()) {
        directory.createSync(recursive: true);
      }

      final filePath = path.join(directory.path, 'data.json');
      final file = File(filePath);

      await file.writeAsString(jsonEncode(jsonData), encoding: utf8);

      final fileSizeBytes = await file.length();
      final fileSizeKB = fileSizeBytes / 1024;
      _logger.info(
          'JSON file saved at $filePath with size: ${fileSizeKB.toStringAsFixed(2)} KB');
    } catch (e) {
      _logger.severe('Error saving JSON file: $e');
    }
  }

  Future<void> _crearArchivoTar() async {
    try {
      // Ruta absoluta del directorio a comprimir
      final directoryPath = 'C:/FlutterProjects/appmanager/Galeria medicapp';
      final tarFilePath =
          'C:/FlutterProjects/appmanager/Galeria medicapp/galeria.tar';

      // Verifica si el directorio existe
      final directory = Directory(directoryPath);
      if (!directory.existsSync()) {
        throw Exception('El directorio no existe: $directoryPath');
      }

      // Imprime la ruta del directorio para depuración
      debugPrint('Directorio a comprimir: $directoryPath');

      // Lógica para crear el archivo TAR
      final result = await Process.run(
          'tar', ['-cvf', tarFilePath, '-C', directoryPath, '.']);

      if (result.exitCode == 0) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Archivo TAR creado exitosamente')),
          );
        }
        // Recargar archivos para mostrar el nuevo archivo TAR
        await _cargarArchivos();
      } else {
        throw Exception('Error al crear archivo TAR: ${result.stderr}');
      }
    } catch (e) {
      debugPrint('Error al crear archivo TAR: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al crear archivo TAR')),
        );
      }
    }
  }

  Future<void> _eliminarArchivo(String fileName) async {
    final supabase = Supabase.instance.client;

    try {
      // Asegurarse de usar solo el nombre del archivo
      final cleanFileName = fileName.split('/').last.split('\\').last;
      debugPrint('Intentando eliminar archivo: $cleanFileName');

      // Intentar eliminar el archivo del bucket de almacenamiento usando la ruta completa
      try {
        final List<FileObject> files =
            await supabase.storage.from('galeria').list();
        final fileExists = files.any((file) => file.name == cleanFileName);

        if (fileExists) {
          final response =
              await supabase.storage.from('galeria').remove([cleanFileName]);
          debugPrint('Respuesta de la operación de eliminación: $response');
        } else {
          debugPrint('El archivo no existe en el bucket: $cleanFileName');
        }
      } catch (e) {
        debugPrint('Error al intentar eliminar el archivo del bucket: $e');
      }

      // Eliminar el archivo localmente
      final localFilePath =
          'C:/FlutterProjects/appmanager/Galeria medicapp/$cleanFileName';
      final localFile = File(localFilePath);
      if (localFile.existsSync()) {
        localFile.deleteSync();
        debugPrint('Archivo local eliminado: $localFilePath');
      } else {
        debugPrint('El archivo local no existe: $localFilePath');
      }

      // Actualizar la lista de archivos
      await _cargarArchivos();

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Archivo eliminado exitosamente')),
      );
    } catch (e) {
      debugPrint('Error al eliminar archivo: $e');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error al eliminar archivo')),
      );
    }
  }

  Future<void> _subirArchivo(String filePath) async {
    final supabase = Supabase.instance.client;
    final file = File(filePath);

    setState(() {
      _uploadFuture = _uploadFile(supabase, file);
    });

    try {
      await _uploadFuture;
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Archivo subido exitosamente')),
      );
    } catch (e) {
      debugPrint('Error al subir archivo: $e');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error al subir archivo')),
      );
    } finally {
      setState(() {
        _uploadFuture = null;
        _uploadProgress = 0.0;
        _uploadSpeed = 0.0;
        _sentSize = 0;
        _totalSize = 0;
      });
    }
  }

  Future<void> _subirArchivoCloudflareR2(String filePath) async {
    final file = File(filePath);
    final fileName = file.uri.pathSegments.last;

    final accessKey = dotenv.env['CF_R2_ACCESS_KEY'];
    final secretKey = dotenv.env['CF_R2_SECRET_KEY'];
    final bucketName = dotenv.env['CF_R2_BUCKET_NAME'];
    final endpoint = dotenv.env['CF_R2_ENDPOINT'];

    if (accessKey == null ||
        secretKey == null ||
        bucketName == null ||
        endpoint == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Cloudflare R2 credentials are not set')),
      );
      return;
    }

    setState(() {
      _uploadFuture = _uploadFileToCloudflareR2(
          file, fileName, accessKey, secretKey, bucketName, endpoint);
    });

    try {
      await _uploadFuture;
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Archivo subido exitosamente a Cloudflare R2')),
      );
    } catch (e) {
      debugPrint('Error al subir archivo a Cloudflare R2: $e');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error al subir archivo a Cloudflare R2')),
      );
    } finally {
      setState(() {
        _uploadFuture = null;
        _uploadProgress = 0.0;
        _uploadSpeed = 0.0;
        _sentSize = 0;
        _totalSize = 0;
      });
    }
  }

  Future<void> _uploadFileToCloudflareR2(
      File file,
      String fileName,
      String accessKey,
      String secretKey,
      String bucketName,
      String endpoint) async {
    _lastSent = 0;
    _lastTime = DateTime.now();
    _totalSize = await file.length();

    final dioClient = dio.Dio();

    // Parse the endpoint to get the host
    final uri = Uri.parse(endpoint);
    final host = uri.host;
    final region = 'auto'; // Cloudflare R2 uses 'auto' as region

    // Prepare the request with proper date formatting
    final now = DateTime.now().toUtc();
    final dateStamp =
        '${now.year.toString().padLeft(4, '0')}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    final amzDate =
        '${now.year.toString().padLeft(4, '0')}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}T${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}Z';

    final url = '$endpoint/$bucketName/$fileName';

    // Create canonical request
    final method = 'PUT';
    final canonicalUri = '/$bucketName/$fileName';
    final canonicalQueryString = '';

    final payloadHash = 'UNSIGNED-PAYLOAD';

    // Include content-length in canonical headers
    final canonicalHeaders = 'content-length:$_totalSize\n'
        'content-type:application/octet-stream\n'
        'host:$host\n'
        'x-amz-content-sha256:$payloadHash\n'
        'x-amz-date:$amzDate\n';

    final signedHeaders =
        'content-length;content-type;host;x-amz-content-sha256;x-amz-date';

    final canonicalRequest = '$method\n'
        '$canonicalUri\n'
        '$canonicalQueryString\n'
        '$canonicalHeaders\n'
        '$signedHeaders\n'
        '$payloadHash';

    // Create string to sign
    final algorithm = 'AWS4-HMAC-SHA256';
    final credentialScope = '$dateStamp/$region/s3/aws4_request';
    final stringToSign = '$algorithm\n'
        '$amzDate\n'
        '$credentialScope\n'
        '${sha256.convert(utf8.encode(canonicalRequest)).toString()}';

    // Calculate signature
    final kDate = _hmacSha256(utf8.encode('AWS4$secretKey'), dateStamp);
    final kRegion = _hmacSha256(kDate, region);
    final kService = _hmacSha256(kRegion, 's3');
    final kSigning = _hmacSha256(kService, 'aws4_request');
    final signature = hex.encode(_hmacSha256(kSigning, stringToSign));

    // Create authorization header
    final authorization = '$algorithm '
        'Credential=$accessKey/$credentialScope, '
        'SignedHeaders=$signedHeaders, '
        'Signature=$signature';

    print('Date stamp: $dateStamp');
    print('AMZ Date: $amzDate');
    print('File size: $_totalSize bytes');
    print('Cloudflare R2 upload URL: $url');
    print('Authorization: $authorization');

    final fileStream = file.openRead();

    try {
      final response = await dioClient.put(
        url,
        data: fileStream,
        options: dio.Options(
          headers: {
            'Host': host,
            'Authorization': authorization,
            'x-amz-content-sha256': payloadHash,
            'x-amz-date': amzDate,
            'Content-Type': 'application/octet-stream',
            'Content-Length': _totalSize.toString(),
          },
          contentType: 'application/octet-stream',
          validateStatus: (status) {
            return status! <
                500; // Don't throw for 4xx errors, we'll handle them
          },
        ),
        onSendProgress: (int sent, int total) {
          final currentTime = DateTime.now();
          final timeDiff = currentTime.difference(_lastTime!).inMilliseconds;
          final dataDiff = sent - _lastSent;

          if (timeDiff > 0) {
            _uploadSpeed = (dataDiff / timeDiff) * 1000 / 1024; // KB/s
          }

          _lastSent = sent;
          _lastTime = currentTime;

          setState(() {
            _uploadProgress = sent / total;
            _sentSize = sent;
          });
        },
      );

      print('Response status: ${response.statusCode}');
      print('Response data: ${response.data}');

      if (response.statusCode != 200 && response.statusCode != 201) {
        throw Exception(
            'Error al subir archivo a Cloudflare R2: ${response.statusCode} ${response.statusMessage}\nResponse: ${response.data}');
      }
    } catch (e) {
      print('Upload error: $e');
      rethrow;
    }
  }

// Helper method for HMAC-SHA256
  List<int> _hmacSha256(List<int> key, String data) {
    final hmac = Hmac(sha256, key);
    return hmac.convert(utf8.encode(data)).bytes;
  }

  Future<void> _uploadFile(SupabaseClient supabase, File file) async {
    final fileName =
        file.uri.pathSegments.last; // Obtener solo el nombre del archivo
    _lastSent = 0;
    _lastTime = DateTime.now();
    _totalSize = await file.length();

    final dioClient = dio.Dio();

    final formData = dio.FormData.fromMap({
      'file': await dio.MultipartFile.fromFile(file.path, filename: fileName),
    });

    final response = await dioClient.post(
      '${dotenv.env['SUPABASE_URL']}/storage/v1/object/galeria/$fileName',
      data: formData,
      options: dio.Options(
        headers: {
          'Authorization':
              'Bearer ${supabase.auth.currentSession?.accessToken}',
          'apikey': dotenv.env['SUPABASE_ANON_KEY'],
        },
      ),
      onSendProgress: (int sent, int total) {
        final currentTime = DateTime.now();
        final timeDiff = currentTime.difference(_lastTime!).inMilliseconds;
        final dataDiff = sent - _lastSent;

        if (timeDiff > 0) {
          _uploadSpeed = (dataDiff / timeDiff) * 1000 / 1024; // KB/s
        }

        _lastSent = sent;
        _lastTime = currentTime;

        setState(() {
          _uploadProgress = sent / total;
          _sentSize = sent;
        });
      },
    );

    if (response.statusCode != 200) {
      throw Exception('Error al subir archivo: ${response.statusMessage}');
    }
  }

  Future<void> actualizarTablaGaleria() async {
    final supabase = Supabase.instance.client;

    try {
      // Listar archivos en el bucket 'galeria'
      final List<FileObject> files =
          await supabase.storage.from('galeria').list();
      debugPrint(
          'Archivos en el bucket: ${files.map((file) => file.name).toList()}');

      // Limpiar la tabla 'galeria'
      await supabase.from('galeria').delete().neq('id', 0);

      // Insertar nuevos registros en la tabla 'galeria'
      for (var i = 0; i < files.length; i++) {
        final file = files[i];
        final fileLink =
            supabase.storage.from('galeria').getPublicUrl(file.name);
        await supabase.from('galeria').insert({
          'id': i + 1, // Asignar un id incremental
          'created_at': DateTime.now().toIso8601String(),
          'file_name': file.name,
          'file_link': fileLink,
        });
      }

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Tabla galeria actualizada exitosamente')),
      );
    } catch (e) {
      debugPrint('Error al actualizar la tabla galeria: $e');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error al actualizar la tabla galeria')),
      );
    }
  }

  void _aumentarZoom() {
    setState(() {
      _zoomFactor += 0.1;
    });
  }

  void _disminuirZoom() {
    setState(() {
      if (_zoomFactor > 0.1) {
        _zoomFactor -= 0.1;
      }
    });
  }

  void _volverAAlbumes() {
    setState(() {
      mostrarAlbumes = true;
      archivos.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(mostrarAlbumes ? 'Álbumes' : 'Galería de Archivos'),
        leading: !mostrarAlbumes
            ? IconButton(
                icon: Icon(Icons.arrow_back),
                onPressed: _volverAAlbumes,
                tooltip: 'Volver a álbumes', // Leyenda
              )
            : null,
        actions: [
          if (!mostrarAlbumes) ...[
            IconButton(
              icon: Icon(Icons.zoom_in),
              onPressed: _aumentarZoom,
              tooltip: 'Aumentar zoom', // Leyenda
            ),
            IconButton(
              icon: Icon(Icons.zoom_out),
              onPressed: _disminuirZoom,
              tooltip: 'Disminuir zoom', // Leyenda
            ),
          ],
          IconButton(
            icon: Icon(Icons.archive),
            onPressed: _crearArchivoTar,
            tooltip: 'Crear archivo TAR', // Leyenda
          ),
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: actualizarTablaGaleria,
            tooltip: 'Actualizar tabla galería', // Leyenda
          ),
        ],
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : Column(
              children: [
                if (_uploadProgress > 0 && _uploadProgress < 1) ...[
                  LinearProgressIndicator(value: _uploadProgress),
                  Text(
                    'Velocidad de subida: ${_uploadSpeed.toStringAsFixed(2)} KB/s',
                    style: TextStyle(fontSize: 16),
                  ),
                  Text(
                    'Enviado: ${(_sentSize / 1024).toStringAsFixed(2)} KB / ${(_totalSize / 1024).toStringAsFixed(2)} KB',
                    style: TextStyle(fontSize: 16),
                  ),
                  Text(
                    'Tiempo restante: ${((_totalSize - _sentSize) / (_uploadSpeed * 1024)).toStringAsFixed(2)} s',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
                Expanded(
                  child: mostrarAlbumes
                      ? _construirVistaAlbumes()
                      : _construirVistaArchivos(),
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Download offline content',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: 20,
                                  child: _isDownloading
                                      ? const Text('Downloading...')
                                      : null,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 8),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              _isDownloading
                                  ? IconButton(
                                      icon: const Icon(Icons.stop),
                                      onPressed: () {
                                        setState(() {
                                          _isDownloading = false;
                                        });
                                      },
                                    )
                                  : IconButton(
                                      icon: const Icon(Icons.play_arrow),
                                      onPressed: () {
                                        setState(() {
                                          _isDownloading = true;
                                        });
                                        _downloadJsonFile().then((_) {
                                          setState(() {
                                            _isDownloading = false;
                                          });
                                        });
                                      },
                                      iconSize: 24.0,
                                    ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                          'Last update: ${DateTime.now().toString().substring(0, 16)}'),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _construirVistaAlbumes() {
    return archivos.isEmpty
        ? Center(child: Text('No se encontraron álbumes'))
        : ListView.builder(
            itemCount: archivos.length,
            itemBuilder: (context, index) {
              final FileSystemEntity archivo = archivos[index];
              if (archivo is Directory) {
                return ListTile(
                  leading: Icon(Icons.folder),
                  title: Text(archivo.path.split('/').last),
                  onTap: () => _cargarArchivosDeAlbum(archivo),
                  subtitle: Text('Directorio'), // Leyenda
                );
              } else if (archivo is File) {
                final fileSizeBytes = archivo.lengthSync();
                final fileSizeKB = fileSizeBytes / 1024;
                final fileSizeMB = fileSizeKB / 1024;

                return ListTile(
                  leading: Icon(Icons.insert_drive_file),
                  title: Text(archivo.path.split('/').last),
                  subtitle: Text(
                    fileSizeMB >= 1
                        ? 'Tamaño: ${fileSizeMB.toStringAsFixed(2)} MB'
                        : 'Tamaño: ${fileSizeKB.toStringAsFixed(2)} KB',
                  ), // Mostrar el tamaño del archivo
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(Icons.cloud_upload),
                        onPressed: () => _subirArchivo(archivo.path),
                        tooltip: 'Subir archivo', // Leyenda
                      ),
                      IconButton(
                        icon: Icon(Icons.cloud_upload),
                        onPressed: () =>
                            _subirArchivoCloudflareR2(archivo.path),
                        tooltip: 'Subir archivo a Cloudflare R2', // Leyenda
                        color: Colors.orange,
                      ),
                      IconButton(
                        icon: Icon(Icons.delete),
                        onPressed: () =>
                            _eliminarArchivo(archivo.path.split('/').last),
                        tooltip: 'Eliminar archivo', // Leyenda
                      ),
                    ],
                  ),
                );
              } else {
                return Container();
              }
            },
          );
  }

  Widget _construirVistaArchivos() {
    return archivos.isEmpty
        ? Center(child: Text('No se encontraron archivos'))
        : GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: _zoomFactor,
            ),
            itemCount: archivos.length,
            itemBuilder: (context, index) {
              final FileSystemEntity archivo = archivos[index];
              if (archivo is File) {
                return GestureDetector(
                  onTap: () {
                    // Aquí puedes manejar la acción al hacer clic en un archivo
                  },
                  child: Column(
                    children: [
                      Icon(Icons.insert_drive_file),
                      Text(archivo.path.split('/').last),
                      Text('Archivo'), // Leyenda
                      FutureBuilder<void>(
                        future: _uploadFuture,
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return CircularProgressIndicator();
                          } else {
                            return Column(
                              children: [
                                LinearProgressIndicator(value: _uploadProgress),
                                IconButton(
                                  icon: Icon(Icons.cloud_upload),
                                  onPressed: () => _subirArchivo(archivo.path),
                                  tooltip: 'Subir archivo', // Leyenda
                                ),
                                IconButton(
                                  icon: Icon(Icons.delete),
                                  onPressed: () => _eliminarArchivo(
                                      archivo.path.split('/').last),
                                  tooltip: 'Eliminar archivo', // Leyenda
                                ),
                              ],
                            );
                          }
                        },
                      ),
                    ],
                  ),
                );
              } else {
                return Container();
              }
            },
          );
  }

  Future<void> _cargarArchivosDeAlbum(Directory album) async {
    setState(() {
      isLoading = true;
    });

    try {
      final List<FileSystemEntity> archivos =
          await compute(_listarArchivosYDirectorios, album);

      if (!mounted) return;

      setState(() {
        this.archivos = archivos;
        mostrarAlbumes = false;
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Error al cargar archivos del álbum: $e');
      if (!mounted) return;
      setState(() {
        isLoading = false;
      });
    }
  }
}
