import 'package:appmanager/usuario_info.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ListaUsuariosPage extends StatefulWidget {
  const ListaUsuariosPage({super.key});

  @override
  ListaUsuariosPageState createState() => ListaUsuariosPageState();
}

class ListaUsuariosPageState extends State<ListaUsuariosPage> {
  late final SupabaseClient _supabaseClient;
  final ValueNotifier<List<dynamic>> _usuariosNotifier =
      ValueNotifier<List<dynamic>>([]);

  @override
  void initState() {
    super.initState();
    _supabaseClient = Supabase.instance.client;
    _fetchUsuarios();
  }

  Future<void> _fetchUsuarios() async {
    try {
      final response = await _supabaseClient
          .from('profiles')
          .select(
              'user_id, username, full_name, role, email, avatar_url, roles(role_name)')
          .order('role');

      _usuariosNotifier.value = response;
    } catch (error) {
      _usuariosNotifier.value = [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lista de Usuarios'),
      ),
      body: ValueListenableBuilder<List<dynamic>>(
        valueListenable: _usuariosNotifier,
        builder: (context, usuarios, _) {
          if (usuarios.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }

          return ListView.builder(
            itemCount: usuarios.length,
            itemBuilder: (context, index) {
              final usuario = usuarios[index];

              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                child: ListTile(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => UsuarioInfo(usuario: usuario),
                      ),
                    );
                  },
                  leading: usuario['avatar_url'] != null
                      ? CircleAvatar(
                          backgroundImage: NetworkImage(usuario['avatar_url']),
                        )
                      : const CircleAvatar(
                          child: Icon(Icons.person),
                        ),
                  title: Text(
                    usuario['full_name'] ?? usuario['username'] ?? 'Sin nombre',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Usuario: ${usuario['username'] ?? 'Sin username'}'),
                      Text('Email: ${usuario['email'] ?? 'Sin email'}'),
                      Text(
                          'Rol: ${usuario['roles']['role_name'] ?? 'Sin rol asignado'}'),
                    ],
                  ),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.edit, color: Colors.blue),
                        onPressed: () async {
                          final result = await Navigator.pushNamed(
                            context,
                            '/usuario_edit',
                            arguments: usuario,
                          );

                          if (result == true) {
                            _fetchUsuarios();
                          }
                        },
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () {
                          // Add delete functionality here
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
