import 'dart:io';
import 'dart:convert';

void main() async {
  print('🔄 Actualizando diccionario completo final...');

  // Detectar si estamos en el directorio scripts o en el directorio raíz
  final currentDir = Directory.current.path;
  final isInScriptsDir = currentDir.endsWith('scripts');
  final assetsPath = isInScriptsDir ? '../assets' : 'assets';

  // PASO 1: Cargar diccionario completo final existente
  print('\n📖 PASO 1: Cargando diccionario completo final...');

  final archivoCompleto = File('$assetsPath/diccionario_completo_final.json');

  if (!await archivoCompleto.exists()) {
    print('❌ Error: No se encuentra diccionario_completo_final.json');
    print(
        '   El archivo base no existe. Ejecuta primero el procesamiento inicial.');
    return;
  }

  final contenidoCompleto = await archivoCompleto.readAsString();
  final dataCompleto = json.decode(contenidoCompleto) as Map<String, dynamic>;
  final palabrasBase = (dataCompleto['palabras'] as List<dynamic>)
      .map((p) => p.toString())
      .toSet();

  print('✅ Diccionario base: ${palabrasBase.length} palabras');

  // PASO 2: Cargar palabras personalizadas de SharedPreferences
  print('\n👤 PASO 2: Buscando palabras personalizadas...');

  final archivoPrefs = File(
      'C:/Users/<USER>/AppData/Roaming/com.example/appmanager/shared_preferences.json');
  Set<String> palabrasPersonalizadas = {};

  if (await archivoPrefs.exists()) {
    try {
      final contenidoPrefs = await archivoPrefs.readAsString();
      final dataPrefs = json.decode(contenidoPrefs) as Map<String, dynamic>;

      // Buscar la clave del diccionario personalizado
      final clavesDiccionario = dataPrefs.keys
          .where((key) => key.contains('diccionario_personalizado'))
          .toList();

      if (clavesDiccionario.isNotEmpty) {
        final clave = clavesDiccionario.first;
        final listaPalabras = dataPrefs[clave] as List<dynamic>?;

        if (listaPalabras != null) {
          palabrasPersonalizadas = listaPalabras
              .map((p) => p
                  .toString()
                  .trim()) // Solo limpiar espacios, preservar mayúsculas
              .where((p) => p.isNotEmpty && p.length >= 2)
              .toSet();

          print(
              '✅ Palabras personalizadas encontradas: ${palabrasPersonalizadas.length}');

          // Mostrar algunas palabras personalizadas
          if (palabrasPersonalizadas.isNotEmpty) {
            final ejemplos = palabrasPersonalizadas.take(10).join(', ');
            print('   Ejemplos: $ejemplos');
          }
        }
      } else {
        print(
            '⚠️ No se encontró diccionario personalizado en SharedPreferences');
      }
    } catch (e) {
      print('❌ Error leyendo SharedPreferences: $e');
    }
  } else {
    print('⚠️ Archivo SharedPreferences no encontrado');
  }

  // PASO 3: Combinar diccionario base con palabras personalizadas
  print('\n🔧 PASO 3: Combinando diccionarios...');
  final totalAntesCombinar =
      palabrasBase.length + palabrasPersonalizadas.length;

  // Combinar usando Set (elimina duplicados automáticamente)
  final palabrasCombinadas = <String>{};
  palabrasCombinadas.addAll(palabrasBase);
  palabrasCombinadas.addAll(palabrasPersonalizadas);

  final duplicadosEliminados = totalAntesCombinar - palabrasCombinadas.length;

  print('📊 Estadísticas combinación:');
  print('   - Diccionario base: ${palabrasBase.length}');
  print('   - Palabras personalizadas: ${palabrasPersonalizadas.length}');
  print('   - Total antes: $totalAntesCombinar');
  print('   - Duplicados eliminados: $duplicadosEliminados');
  print('   - Total después: ${palabrasCombinadas.length}');

  // Debug: Verificar si las palabras personalizadas están en el resultado
  if (palabrasPersonalizadas.isNotEmpty) {
    print('\n🔍 Verificando integración de palabras personalizadas:');
    for (final palabra in palabrasPersonalizadas.take(5)) {
      final integrada = palabrasCombinadas.contains(palabra);
      print('   - "$palabra": ${integrada ? "✅ Integrada" : "❌ No integrada"}');
    }
  }

  // PASO 4: Ordenamiento alfabético
  print('\n🔤 PASO 4: Ordenando alfabéticamente...');
  final palabrasFinales = palabrasCombinadas.toList()..sort();

  // PASO 5: Crear JSON final
  print('\n💾 PASO 5: Generando diccionario completo final...');
  final diccionarioFinal = {
    'version': 'diccionario_completo_final_v1',
    'fuentes': [
      'Diccionario Completo Base',
      'Palabras Personalizadas del Usuario'
    ],
    'fecha_creacion': DateTime.now().toIso8601String(),
    'fecha_combinacion': DateTime.now().toIso8601String(),
    'palabras': palabrasFinales,
    'total_palabras': palabrasFinales.length,
    'estadisticas': {
      'diccionario_base': palabrasBase.length,
      'palabras_personalizadas': palabrasPersonalizadas.length,
      'duplicados_eliminados': duplicadosEliminados,
      'total_final': palabrasFinales.length,
    },
    'metadatos': {
      'ordenamiento': 'alfabetico',
      'normalizacion': 'minusculas_sin_espacios',
      'filtrado': 'palabras_validas_espanol',
    }
  };

  // PASO 6: Guardar archivo final
  final archivoFinal = File('$assetsPath/diccionario_completo_final.json');
  await archivoFinal.writeAsString(json.encode(diccionarioFinal));

  print('✅ Diccionario completo final guardado en: ${archivoFinal.path}');

  // PASO 7: Verificación final
  print('\n🧪 PASO 7: Verificación de palabras clave:');
  final palabrasPrueba = [
    'animal', 'animales', 'común', 'comunes', 'médico', 'médica',
    'evaluación', 'evaluaciones', 'demuestran', 'desarrollan', 'establecen',
    'realizan', 'presentan', 'consideran', 'analizan', 'estudian',
    // Términos médicos en minúsculas
    'articulación', 'articulaciones', 'diagnóstico', 'tratamiento',
    'síntoma', 'síntomas', 'patología', 'anatomía', 'fisiología',
    // Términos médicos con mayúsculas (preservadas)
    'Alzheimer', 'Parkinson', 'VIH', 'SIDA', 'ECG', 'Articulación'
  ];

  int palabrasEncontradas = 0;
  for (final palabra in palabrasPrueba) {
    final existe = palabrasFinales.contains(palabra);
    if (existe) palabrasEncontradas++;
    print('   - "$palabra": ${existe ? "✅" : "❌"}');
  }

  // PASO 8: Resumen final
  print('\n📈 RESUMEN FINAL:');
  print(
      '   🔤 Palabras verificadas: $palabrasEncontradas/${palabrasPrueba.length}');
  print('   📊 Total diccionario final: ${palabrasFinales.length} palabras');
  print(
      '   📈 Incremento: +${palabrasPersonalizadas.length - duplicadosEliminados} palabras nuevas');
  print('   🎯 Ordenamiento: Alfabético completo');
  print('   💾 Archivo: diccionario_completo_final.json');
  print('   ✅ Combinación completada exitosamente');

  // Sugerencia para la app
  print('\n💡 SIGUIENTE PASO:');
  print(
      '   Actualiza la app para usar: assets/diccionario_completo_final.json');
}
