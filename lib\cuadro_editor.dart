import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

enum ContenidoTipo { texto, listaDesordenada }

class ContenidoElemento {
  ContenidoTipo tipo;
  String texto;

  ContenidoElemento({required this.tipo, required this.texto});
}

class CeldaData {
  List<ContenidoElemento> elementos = [];
  Color colorFondo = Colors.grey[600]!;

  // Lista de elementos que pueden ser texto o viñetas
}

class CuadroEditor extends StatefulWidget {
  final int? cuadroId;
  final int? leccionId; // Para crear la relación automáticamente

  const CuadroEditor({
    super.key,
    this.cuadroId,
    this.leccionId,
  });

  @override
  State<CuadroEditor> createState() => _CuadroEditorState();
}

class _CuadroEditorState extends State<CuadroEditor> {
  final TextEditingController _tituloController = TextEditingController();
  int _columnas = 1;
  int _filas = 1;
  bool _isLoading = false;
  bool _isEditing = false;
  Color _colorCuadroGlobal = Colors.grey[600]!; // Color global por defecto
  int? _celdaSeleccionadaFila;
  int? _celdaSeleccionadaColumna;
  int? _columnaSeleccionada; // Para selección de columna completa
  String? _contenidoCopiado; // Para funcionalidad de copiar/pegar

  // Estados para controlar la visibilidad de los selectores con hover
  int? _columnaHover; // Columna sobre la que está el mouse
  int? _filaHover; // Fila sobre la que está el mouse

  // Matriz para almacenar el contenido de cada celda
  List<List<CeldaData>> _matriz = [];

  @override
  void initState() {
    super.initState();
    _inicializarMatriz();
    // Seleccionar automáticamente la primera celda
    _celdaSeleccionadaFila = 0;
    _celdaSeleccionadaColumna = 0;
    if (widget.cuadroId != null) {
      _cargarCuadro();
    }
  }

  void _inicializarMatriz() {
    _matriz = List.generate(_filas, (fila) {
      return List.generate(_columnas, (columna) {
        final celda = CeldaData();
        celda.colorFondo = _colorCuadroGlobal;
        // Agregar un elemento de texto por defecto
        celda.elementos.add(ContenidoElemento(
          tipo: ContenidoTipo.texto,
          texto: '',
        ));
        return celda;
      });
    });
  }

  Future<void> _cargarCuadro() async {
    if (widget.cuadroId == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await Supabase.instance.client
          .from('cuadros')
          .select()
          .eq('id', widget.cuadroId!)
          .single();

      _tituloController.text = response['titulo'] ?? '';

      if (response['contenido'] != null) {
        final contenidoData = response['contenido'] as Map<String, dynamic>;
        _filas = contenidoData['filas'] ?? 2;
        _columnas = contenidoData['columnas'] ?? 2;

        // Cargar color si existe
        if (contenidoData['color'] != null) {
          _colorCuadroGlobal = Color(contenidoData['color'] as int);
        }

        _inicializarMatriz();

        if (contenidoData['matriz'] != null) {
          final matrizData = contenidoData['matriz'] as List;
          for (int fila = 0;
              fila < _filas && fila < matrizData.length;
              fila++) {
            final filaData = matrizData[fila] as List;
            for (int columna = 0;
                columna < _columnas && columna < filaData.length;
                columna++) {
              final celdaData = filaData[columna] as Map<String, dynamic>;
              final elementos = celdaData['elementos'] as List<dynamic>? ?? [];
              _matriz[fila][columna].elementos = elementos.map((e) {
                final elementData = e as Map<String, dynamic>;
                return ContenidoElemento(
                  tipo: elementData['tipo'] == 'listaDesordenada'
                      ? ContenidoTipo.listaDesordenada
                      : ContenidoTipo.texto,
                  texto: elementData['texto'] ?? '',
                );
              }).toList();

              // Cargar color individual de la celda si existe
              if (celdaData['colorFondo'] != null) {
                _matriz[fila][columna].colorFondo =
                    Color(celdaData['colorFondo'] as int);
              }
            }
          }
        }
      }

      setState(() {
        _isEditing = true;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al cargar cuadro: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _guardarCuadro() async {
    if (_tituloController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('El título es obligatorio')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final contenidoData = {
        'filas': _filas,
        'columnas': _columnas,
        'color': _colorCuadroGlobal.toARGB32(), // Guardar color como int
        'matriz': _matriz.map((fila) {
          return fila.map((celda) {
            return {
              'elementos': celda.elementos.map((elemento) {
                return {
                  'tipo': elemento.tipo == ContenidoTipo.listaDesordenada
                      ? 'listaDesordenada'
                      : 'texto',
                  'texto': elemento.texto,
                };
              }).toList(),
              'colorFondo':
                  celda.colorFondo.toARGB32(), // Guardar color individual
            };
          }).toList();
        }).toList(),
      };

      if (_isEditing && widget.cuadroId != null) {
        await Supabase.instance.client.from('cuadros').update({
          'titulo': _tituloController.text.trim(),
          'contenido': contenidoData,
        }).eq('id', widget.cuadroId!);
      } else {
        final response = await Supabase.instance.client
            .from('cuadros')
            .insert({
              'titulo': _tituloController.text.trim(),
              'contenido': contenidoData,
            })
            .select()
            .single();

        if (widget.leccionId != null) {
          await Supabase.instance.client.from('leccion_cuadros').insert({
            'leccion_id': widget.leccionId!,
            'cuadro_id': response['id'],
          });
        }
      }

      if (mounted) {
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al guardar: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _agregarFila() {
    setState(() {
      _filas++;
      _matriz.add(List.generate(_columnas, (columna) => CeldaData()));
    });
  }

  void _agregarColumna() {
    setState(() {
      _columnas++;
      for (int fila = 0; fila < _filas; fila++) {
        _matriz[fila].add(CeldaData());
      }
    });
  }

  void _eliminarFila(int index) {
    if (_filas > 1) {
      setState(() {
        _filas--;
        _matriz.removeAt(index);
      });
    }
  }

  void _eliminarColumna(int index) {
    if (_columnas > 1) {
      setState(() {
        _columnas--;
        for (int fila = 0; fila < _filas; fila++) {
          _matriz[fila].removeAt(index);
        }
        // Ajustar selecciones
        if (_celdaSeleccionadaColumna != null &&
            _celdaSeleccionadaColumna! >= index) {
          _celdaSeleccionadaColumna = _celdaSeleccionadaColumna! > index
              ? _celdaSeleccionadaColumna! - 1
              : null;
        }
        if (_columnaSeleccionada != null && _columnaSeleccionada! >= index) {
          _columnaSeleccionada =
              _columnaSeleccionada! > index ? _columnaSeleccionada! - 1 : null;
        }
      });
    }
  }

  // Funciones del menú contextual
  void _mostrarSelectorColorFila(int fila) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Seleccionar color para fila ${fila + 1}'),
        content: Wrap(
          children: [
            for (Color color in [
              Colors.white,
              Colors.red[100]!,
              Colors.blue[100]!,
              Colors.green[100]!,
              Colors.yellow[100]!,
              Colors.purple[100]!,
              Colors.orange[100]!,
              Colors.grey[100]!,
            ])
              GestureDetector(
                onTap: () {
                  setState(() {
                    for (int col = 0; col < _columnas; col++) {
                      _matriz[fila][col].colorFondo = color;
                    }
                  });
                  Navigator.pop(context);
                },
                child: Container(
                  width: 40,
                  height: 40,
                  margin: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color,
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _insertarFilaArriba(int index) {
    setState(() {
      _filas++;
      _matriz.insert(index, List.generate(_columnas, (columna) => CeldaData()));
      if (_celdaSeleccionadaFila != null && _celdaSeleccionadaFila! >= index) {
        _celdaSeleccionadaFila = _celdaSeleccionadaFila! + 1;
      }
    });
  }

  void _insertarFilaAbajo(int index) {
    setState(() {
      _filas++;
      _matriz.insert(
          index + 1, List.generate(_columnas, (columna) => CeldaData()));
    });
  }

  void _duplicarFila(int index) {
    setState(() {
      _filas++;
      List<CeldaData> filaDuplicada = [];
      for (var celda in _matriz[index]) {
        CeldaData nuevaCelda = CeldaData();
        nuevaCelda.colorFondo = celda.colorFondo;
        for (var elemento in celda.elementos) {
          nuevaCelda.elementos.add(ContenidoElemento(
            tipo: elemento.tipo,
            texto: elemento.texto,
          ));
        }
        filaDuplicada.add(nuevaCelda);
      }
      _matriz.insert(index + 1, filaDuplicada);
    });
  }

  void _limpiarContenidoFila(int index) {
    setState(() {
      for (int col = 0; col < _columnas; col++) {
        _matriz[index][col].elementos.clear();
        _matriz[index][col]
            .elementos
            .add(ContenidoElemento(tipo: ContenidoTipo.texto, texto: ''));
      }
    });
  }

  // Funciones del menú contextual de columna
  void _mostrarSelectorColorColumna(int columna) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Seleccionar color para columna ${columna + 1}'),
        content: Wrap(
          children: [
            for (Color color in [
              Colors.white,
              Colors.red[100]!,
              Colors.blue[100]!,
              Colors.green[100]!,
              Colors.yellow[100]!,
              Colors.purple[100]!,
              Colors.orange[100]!,
              Colors.grey[100]!,
            ])
              GestureDetector(
                onTap: () {
                  setState(() {
                    for (int fila = 0; fila < _filas; fila++) {
                      _matriz[fila][columna].colorFondo = color;
                    }
                  });
                  Navigator.pop(context);
                },
                child: Container(
                  width: 40,
                  height: 40,
                  margin: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color,
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _insertarColumnaIzquierda(int index) {
    setState(() {
      _columnas++;
      for (int fila = 0; fila < _filas; fila++) {
        _matriz[fila].insert(index, CeldaData());
      }
      // Ajustar selección
      if (_celdaSeleccionadaColumna != null &&
          _celdaSeleccionadaColumna! >= index) {
        _celdaSeleccionadaColumna = _celdaSeleccionadaColumna! + 1;
      }
      if (_columnaSeleccionada != null && _columnaSeleccionada! >= index) {
        _columnaSeleccionada = _columnaSeleccionada! + 1;
      }
    });
  }

  void _insertarColumnaDerecha(int index) {
    setState(() {
      _columnas++;
      for (int fila = 0; fila < _filas; fila++) {
        _matriz[fila].insert(index + 1, CeldaData());
      }
    });
  }

  void _duplicarColumna(int index) {
    setState(() {
      _columnas++;
      for (int fila = 0; fila < _filas; fila++) {
        CeldaData celdaOriginal = _matriz[fila][index];
        CeldaData nuevaCelda = CeldaData();
        nuevaCelda.colorFondo = celdaOriginal.colorFondo;
        for (var elemento in celdaOriginal.elementos) {
          nuevaCelda.elementos.add(ContenidoElemento(
            tipo: elemento.tipo,
            texto: elemento.texto,
          ));
        }
        _matriz[fila].insert(index + 1, nuevaCelda);
      }
    });
  }

  void _limpiarContenidoColumna(int index) {
    setState(() {
      for (int fila = 0; fila < _filas; fila++) {
        _matriz[fila][index].elementos.clear();
        _matriz[fila][index]
            .elementos
            .add(ContenidoElemento(tipo: ContenidoTipo.texto, texto: ''));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Editar Cuadro' : 'Nuevo Cuadro'),
        actions: [
          if (_isLoading)
            const Center(child: CircularProgressIndicator())
          else
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _guardarCuadro,
              tooltip: 'Guardar cuadro',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildEditor(),
    );
  }

  Widget _buildEditor() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Campo título
          TextField(
            controller: _tituloController,
            decoration: const InputDecoration(
              labelText: 'Título del cuadro',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),

          // Selector de color
          _buildColorSelector(),
          const SizedBox(height: 16),

          // Editor de tabla
          Expanded(child: _buildTableEditor()),
        ],
      ),
    );
  }

  Widget _buildColorSelector() {
    final colores = [
      Colors.grey[600]!,
      Colors.blue[600]!,
      Colors.green[600]!,
      Colors.red[600]!,
      Colors.orange[600]!,
      Colors.purple[600]!,
      Colors.teal[600]!,
      Colors.brown[600]!,
    ];

    return Row(
      children: [
        const Text('Color: '),
        const SizedBox(width: 8),
        ...colores.map((color) {
          return GestureDetector(
            onTap: () {
              setState(() {
                if (_celdaSeleccionadaFila != null &&
                    _celdaSeleccionadaColumna != null) {
                  // Cambiar color de celda específica
                  _matriz[_celdaSeleccionadaFila!][_celdaSeleccionadaColumna!]
                      .colorFondo = color;
                } else {
                  // Cambiar color global por defecto
                  _colorCuadroGlobal = color;
                  // Aplicar a todas las celdas
                  for (var fila in _matriz) {
                    for (var celda in fila) {
                      celda.colorFondo = color;
                    }
                  }
                }
              });
            },
            child: Container(
              margin: const EdgeInsets.only(right: 8),
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: color,
                border: Border.all(
                  color: _colorCuadroGlobal == color
                      ? Colors.black
                      : Colors.transparent,
                  width: 3,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          );
        }),
        const SizedBox(width: 16),
        // Botones para agregar elementos
        const Text('Agregar: '),
        const SizedBox(width: 8),
        // Botón agregar texto
        InkWell(
          onTap: () {
            setState(() {
              // Si no hay celda seleccionada, seleccionar la primera
              if (_celdaSeleccionadaFila == null ||
                  _celdaSeleccionadaColumna == null) {
                _celdaSeleccionadaFila = 0;
                _celdaSeleccionadaColumna = 0;
              }
              _matriz[_celdaSeleccionadaFila!][_celdaSeleccionadaColumna!]
                  .elementos
                  .add(ContenidoElemento(
                    tipo: ContenidoTipo.texto,
                    texto: '',
                  ));
            });
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey),
            ),
            child: Icon(
              Icons.text_fields,
              size: 20,
              color: Colors.grey[600],
            ),
          ),
        ),
        const SizedBox(width: 8),
        // Botón agregar viñeta
        InkWell(
          onTap: () {
            setState(() {
              // Si no hay celda seleccionada, seleccionar la primera
              if (_celdaSeleccionadaFila == null ||
                  _celdaSeleccionadaColumna == null) {
                _celdaSeleccionadaFila = 0;
                _celdaSeleccionadaColumna = 0;
              }

              final celda =
                  _matriz[_celdaSeleccionadaFila!][_celdaSeleccionadaColumna!];

              // Si hay un elemento de texto vacío, convertirlo a viñeta
              if (celda.elementos.isNotEmpty &&
                  celda.elementos.last.tipo == ContenidoTipo.texto &&
                  celda.elementos.last.texto.isEmpty) {
                celda.elementos.last.tipo = ContenidoTipo.listaDesordenada;
              } else {
                // Agregar nuevo elemento de viñeta
                celda.elementos.add(ContenidoElemento(
                  tipo: ContenidoTipo.listaDesordenada,
                  texto: '',
                ));
              }
            });
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue[100],
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.blue),
            ),
            child: Icon(
              Icons.circle,
              size: 20,
              color: Colors.blue,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Text(
          _celdaSeleccionadaFila != null && _celdaSeleccionadaColumna != null
              ? 'Celda: ${_celdaSeleccionadaFila! + 1},${_celdaSeleccionadaColumna! + 1}'
              : 'Celda: 1,1',
          style: TextStyle(
            fontSize: 12,
            color: Colors.blue[600],
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildTableEditor() {
    return SingleChildScrollView(
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Fila con tabla principal + barra lateral derecha
            Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Tabla principal con selectores flotantes
                Container(
                  margin: EdgeInsets.only(
                      top: 35, left: 35), // Espacio para selectores
                  child: Stack(
                    clipBehavior: Clip
                        .none, // Permitir que los selectores salgan del Stack
                    children: [
                      // Tabla de datos
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          for (int fila = 0; fila < _filas; fila++)
                            IntrinsicHeight(
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  for (int columna = 0;
                                      columna < _columnas;
                                      columna++)
                                    _buildCelda(fila, columna),
                                ],
                              ),
                            ),
                        ],
                      ),
                      // Selectores flotantes de columna
                      for (int columna = 0; columna < _columnas; columna++)
                        _buildSelectorColumnaFlotante(columna),
                      // Selectores flotantes de fila
                      for (int fila = 0; fila < _filas; fila++)
                        _buildSelectorFilaFlotante(fila),
                    ],
                  ),
                ),
                // Barra lateral derecha para agregar columnas
                _buildBarraLateralColumnas(),
              ],
            ),
            // Barra horizontal inferior para agregar filas
            _buildBarraHorizontalFilas(),
          ],
        ),
      ),
    );
  }

  // Funciones del menú contextual de fila (simplificada para selectores flotantes)
  void _mostrarMenuContextualFila(
      BuildContext context, Offset position, int fila) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy,
        position.dx + 1,
        position.dy + 1,
      ),
      items: [
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.palette, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Color'),
            ],
          ),
          onTap: () => _mostrarSelectorColorFila(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.keyboard_arrow_up, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Insert Above'),
            ],
          ),
          onTap: () => _insertarFilaArriba(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.keyboard_arrow_down,
                  size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Insert Below'),
            ],
          ),
          onTap: () => _insertarFilaAbajo(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.content_copy, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Duplicate'),
            ],
          ),
          onTap: () => _duplicarFila(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.clear, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Clear Contents'),
            ],
          ),
          onTap: () => _limpiarContenidoFila(fila),
        ),
        if (_filas > 1)
          PopupMenuItem(
            child: Row(
              children: [
                Icon(Icons.delete, size: 16, color: Colors.red[600]),
                SizedBox(width: 8),
                Text('Delete', style: TextStyle(color: Colors.red[600])),
              ],
            ),
            onTap: () => _eliminarFila(fila),
          ),
      ],
    );
  }

  // Mostrar menú contextual para operaciones de columna
  void _mostrarMenuContextualColumna(
      BuildContext context, Offset position, int columna) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy,
        position.dx + 1,
        position.dy + 1,
      ),
      items: [
        PopupMenuItem(
          enabled: false,
          child: SizedBox(
            width: 200,
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search actions...',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              style: TextStyle(fontSize: 14),
            ),
          ),
        ),
        PopupMenuItem(
          enabled: false,
          child: Text('Table',
              style: TextStyle(
                  fontWeight: FontWeight.bold, color: Colors.grey[600])),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.palette, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Color'),
              Spacer(),
              Icon(Icons.chevron_right, size: 16, color: Colors.grey[400]),
            ],
          ),
          onTap: () => _mostrarSelectorColorColumna(columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.keyboard_arrow_left,
                  size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Insert Left'),
            ],
          ),
          onTap: () => _insertarColumnaIzquierda(columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.keyboard_arrow_right,
                  size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Insert Right'),
            ],
          ),
          onTap: () => _insertarColumnaDerecha(columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.content_copy, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Duplicate'),
              Spacer(),
              Text('Ctrl+D',
                  style: TextStyle(color: Colors.grey[400], fontSize: 12)),
            ],
          ),
          onTap: () => _duplicarColumna(columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.clear, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Clear Contents'),
            ],
          ),
          onTap: () => _limpiarContenidoColumna(columna),
        ),
        if (_columnas > 1)
          PopupMenuItem(
            child: Row(
              children: [
                Icon(Icons.delete, size: 16, color: Colors.red[600]),
                SizedBox(width: 8),
                Text('Delete', style: TextStyle(color: Colors.red[600])),
                Spacer(),
                Text('Del',
                    style: TextStyle(color: Colors.grey[400], fontSize: 12)),
              ],
            ),
            onTap: () => _eliminarColumna(columna),
          ),
      ],
    );
  }

  // Mostrar menú contextual para operaciones de celda individual
  void _mostrarMenuContextualCelda(
      BuildContext context, Offset position, int fila, int columna) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy,
        position.dx + 1,
        position.dy + 1,
      ),
      items: [
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.content_copy, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Copy'),
              Spacer(),
              Text('Ctrl+C',
                  style: TextStyle(color: Colors.grey[400], fontSize: 12)),
            ],
          ),
          onTap: () => _copiarCelda(fila, columna),
        ),
        if (_contenidoCopiado != null)
          PopupMenuItem(
            child: Row(
              children: [
                Icon(Icons.content_paste, size: 16, color: Colors.grey[600]),
                SizedBox(width: 8),
                Text('Paste'),
                Spacer(),
                Text('Ctrl+V',
                    style: TextStyle(color: Colors.grey[400], fontSize: 12)),
              ],
            ),
            onTap: () => _pegarCelda(fila, columna),
          ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.clear, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Clear Contents'),
            ],
          ),
          onTap: () => _limpiarCelda(fila, columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.palette, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Color'),
              Spacer(),
              Icon(Icons.chevron_right, size: 16, color: Colors.grey[400]),
            ],
          ),
          onTap: () => _mostrarSelectorColorCelda(fila, columna),
        ),
      ],
    );
  }

  // Funciones para operaciones de celda individual
  void _copiarCelda(int fila, int columna) {
    final celda = _matriz[fila][columna];
    List<String> contenidos = [];
    for (var elemento in celda.elementos) {
      if (elemento.tipo == ContenidoTipo.listaDesordenada) {
        contenidos.add('• ${elemento.texto}');
      } else {
        contenidos.add(elemento.texto);
      }
    }
    setState(() {
      _contenidoCopiado = contenidos.join('\n');
    });
  }

  void _pegarCelda(int fila, int columna) {
    if (_contenidoCopiado != null) {
      setState(() {
        final celda = _matriz[fila][columna];
        celda.elementos.clear();

        // Dividir el contenido por líneas
        List<String> lineas = _contenidoCopiado!.split('\n');
        for (String linea in lineas) {
          if (linea.trim().startsWith('•')) {
            // Es un bullet point
            String texto = linea.trim().substring(1).trim();
            celda.elementos.add(ContenidoElemento(
                tipo: ContenidoTipo.listaDesordenada, texto: texto));
          } else if (linea.trim().isNotEmpty) {
            // Es texto normal
            celda.elementos.add(ContenidoElemento(
                tipo: ContenidoTipo.texto, texto: linea.trim()));
          }
        }

        // Si no hay elementos, agregar uno vacío
        if (celda.elementos.isEmpty) {
          celda.elementos
              .add(ContenidoElemento(tipo: ContenidoTipo.texto, texto: ''));
        }
      });
    }
  }

  void _limpiarCelda(int fila, int columna) {
    setState(() {
      final celda = _matriz[fila][columna];
      celda.elementos.clear();
      celda.elementos
          .add(ContenidoElemento(tipo: ContenidoTipo.texto, texto: ''));
    });
  }

  void _mostrarSelectorColorCelda(int fila, int columna) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Seleccionar color para celda'),
        content: Wrap(
          children: [
            for (Color color in [
              Colors.white,
              Colors.red[100]!,
              Colors.blue[100]!,
              Colors.green[100]!,
              Colors.yellow[100]!,
              Colors.purple[100]!,
              Colors.orange[100]!,
              Colors.grey[100]!,
            ])
              GestureDetector(
                onTap: () {
                  setState(() {
                    _matriz[fila][columna].colorFondo = color;
                  });
                  Navigator.pop(context);
                },
                child: Container(
                  width: 40,
                  height: 40,
                  margin: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color,
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Barra lateral derecha para agregar columnas (estilo Notion)
  Widget _buildBarraLateralColumnas() {
    return SizedBox(
      width: 30,
      child: Column(
        children: [
          for (int fila = 0; fila < _filas; fila++)
            IntrinsicHeight(
              child: Container(
                constraints: BoxConstraints(minHeight: 40),
                child: fila == 0 // Solo mostrar botón en la primera fila
                    ? MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: GestureDetector(
                          onTap: _agregarColumna,
                          child: Container(
                            width: 30,
                            decoration: BoxDecoration(
                              color: Colors.grey[50],
                              border: Border(
                                top: BorderSide(color: Colors.grey[300]!),
                                right: BorderSide(color: Colors.grey[300]!),
                                bottom: BorderSide(color: Colors.grey[300]!),
                              ),
                            ),
                            child: Center(
                              child: Icon(
                                Icons.add,
                                size: 16,
                                color: Colors.grey[400],
                              ),
                            ),
                          ),
                        ),
                      )
                    : Container(
                        width: 30,
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          border: Border(
                            right: BorderSide(color: Colors.grey[300]!),
                            bottom: BorderSide(color: Colors.grey[300]!),
                          ),
                        ),
                      ),
              ),
            ),
        ],
      ),
    );
  }

  // Barra horizontal inferior para agregar filas (estilo Notion)
  Widget _buildBarraHorizontalFilas() {
    return SizedBox(
      height: 30,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Espacios para cada columna
          for (int columna = 0; columna < _columnas; columna++)
            Container(
              width: 150, // Ancho mínimo de celda
              height: 30,
              decoration: BoxDecoration(
                color: Colors.grey[50],
                border: Border(
                  left: columna == 0
                      ? BorderSide(color: Colors.grey[300]!)
                      : BorderSide.none,
                  right: BorderSide(color: Colors.grey[300]!),
                  bottom: BorderSide(color: Colors.grey[300]!),
                ),
              ),
              child: columna == 0 // Solo mostrar botón en la primera columna
                  ? MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: _agregarFila,
                        child: Icon(
                          Icons.add,
                          size: 16,
                          color: Colors.grey[400],
                        ),
                      ),
                    )
                  : null,
            ),
          // Esquina inferior derecha
          Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(
                right: BorderSide(color: Colors.grey[300]!),
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCelda(int fila, int columna) {
    final celda = _matriz[fila][columna];
    final isSelected =
        _celdaSeleccionadaFila == fila && _celdaSeleccionadaColumna == columna;

    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _columnaHover = columna;
          _filaHover = fila;
        });
      },
      onExit: (_) {
        // Usar un delay más largo para dar tiempo a alcanzar el botón
        Future.delayed(Duration(milliseconds: 300), () {
          setState(() {
            _columnaHover = null;
            _filaHover = null;
          });
        });
      },
      child: GestureDetector(
          onTap: () {
            setState(() {
              _celdaSeleccionadaFila = fila;
              _celdaSeleccionadaColumna = columna;
              // Limpiar selección de columna
              _columnaSeleccionada = null;
            });
          },
          onSecondaryTapDown: (details) {
            setState(() {
              _celdaSeleccionadaFila = fila;
              _celdaSeleccionadaColumna = columna;
              _columnaSeleccionada = null;
            });

            // Detectar si el click fue cerca del borde izquierdo (área de fila)
            RenderBox renderBox = context.findRenderObject() as RenderBox;
            Offset localPosition =
                renderBox.globalToLocal(details.globalPosition);

            if (localPosition.dx < 30) {
              // Click en área de fila - mostrar menú de fila
              _mostrarMenuContextualFila(context, details.globalPosition, fila);
            } else {
              // Click en celda - mostrar menú de celda
              _mostrarMenuContextualCelda(
                  context, details.globalPosition, fila, columna);
            }
          },
          child: Container(
            width: 150, // Ancho fijo para alinearse con la barra inferior
            constraints: const BoxConstraints(
              minHeight: 40, // Altura mínima
            ),
            decoration: BoxDecoration(
              color: celda.colorFondo
                  .withValues(alpha: 0.1), // Fondo del cuadro individual
              border: Border(
                top: BorderSide(
                    color: isSelected ? Colors.blue : Colors.grey[300]!,
                    width: isSelected ? 2.0 : 1.0),
                left: BorderSide(
                    color: isSelected ? Colors.blue : Colors.grey[300]!,
                    width: isSelected ? 2.0 : 1.0),
                right: BorderSide(
                    color: isSelected ? Colors.blue : Colors.grey[300]!,
                    width: isSelected ? 2.0 : 1.0),
                bottom: BorderSide(
                    color: isSelected ? Colors.blue : Colors.grey[300]!,
                    width: isSelected ? 2.0 : 1.0),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(6.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Lista de elementos
                  ...celda.elementos.asMap().entries.map((entry) {
                    final index = entry.key;
                    final elemento = entry.value;

                    return Container(
                      margin: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Indicador de tipo
                          Container(
                            margin: const EdgeInsets.only(top: 8, right: 8),
                            child:
                                elemento.tipo == ContenidoTipo.listaDesordenada
                                    ? Icon(Icons.circle,
                                        size: 8, color: Colors.blue)
                                    : Icon(Icons.text_fields,
                                        size: 12, color: Colors.grey[600]),
                          ),
                          // Campo de texto
                          Expanded(
                            child: TextField(
                              controller:
                                  TextEditingController(text: elemento.texto),
                              maxLines: null,
                              minLines: 1,
                              keyboardType: TextInputType.multiline,
                              textInputAction: TextInputAction.newline,
                              decoration: InputDecoration(
                                hintText: elemento.tipo ==
                                        ContenidoTipo.listaDesordenada
                                    ? 'Lista'
                                    : 'Texto...',
                                border: InputBorder.none,
                                isDense: true,
                              ),
                              style: TextStyle(
                                fontSize: 14,
                                height: 1.3,
                              ),
                              onChanged: (value) {
                                elemento.texto = value;
                              },
                            ),
                          ),
                          // Botón eliminar elemento
                          InkWell(
                            onTap: () {
                              setState(() {
                                celda.elementos.removeAt(index);
                              });
                            },
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              child: Icon(
                                Icons.close,
                                size: 16,
                                color: Colors.red[400],
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            ),
          )),
    );
  }

  // Selector flotante de columna que aparece con hover
  Widget _buildSelectorColumnaFlotante(int columna) {
    final isSelected = _columnaSeleccionada == columna;
    final isVisible = _columnaHover == columna;

    return Positioned(
      left: columna * 150.0 +
          75 -
          12, // Centro de la columna menos la mitad del botón
      top: -12, // Superpuesto con la línea superior de la celda
      child: MouseRegion(
        onEnter: (_) {
          setState(() {
            _columnaHover = columna;
          });
        },
        child: AnimatedOpacity(
          opacity: isVisible ? 1.0 : 0.0,
          duration: Duration(milliseconds: 200),
          child: SizedBox(
            // Área de hover expandida
            width: 40,
            height: 40,
            child: Center(
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(12),
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () {
                    setState(() {
                      _columnaSeleccionada = columna;
                      _celdaSeleccionadaFila = null;
                      _celdaSeleccionadaColumna = null;
                    });
                    // Mostrar menú contextual también con click izquierdo
                    final RenderBox renderBox =
                        context.findRenderObject() as RenderBox;
                    final position = renderBox.localToGlobal(Offset.zero);
                    _mostrarMenuContextualColumna(
                        context, position + Offset(12, 24), columna);
                  },
                  onSecondaryTapDown: (details) {
                    setState(() {
                      _columnaSeleccionada = columna;
                      _celdaSeleccionadaFila = null;
                      _celdaSeleccionadaColumna = null;
                    });
                    _mostrarMenuContextualColumna(
                        context, details.globalPosition, columna);
                  },
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.blue[100] : Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected ? Colors.blue : Colors.grey[300]!,
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.more_horiz,
                      size: 12,
                      color: isSelected ? Colors.blue[700] : Colors.grey[600],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Selector flotante de fila que aparece con hover
  Widget _buildSelectorFilaFlotante(int fila) {
    final isVisible = _filaHover == fila;

    return Positioned(
      left: -12, // Superpuesto con la línea izquierda de la celda
      top: fila * 40.0 + 20 - 12, // Centro de la fila menos la mitad del botón
      child: MouseRegion(
        onEnter: (_) {
          setState(() {
            _filaHover = fila;
          });
        },
        child: AnimatedOpacity(
          opacity: isVisible ? 1.0 : 0.0,
          duration: Duration(milliseconds: 200),
          child: SizedBox(
            // Área de hover expandida
            width: 40,
            height: 40,
            child: Center(
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(12),
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () {
                    // Mostrar menú contextual también con click izquierdo
                    final RenderBox renderBox =
                        context.findRenderObject() as RenderBox;
                    final position = renderBox.localToGlobal(Offset.zero);
                    _mostrarMenuContextualFila(
                        context, position + Offset(24, 12), fila);
                  },
                  onSecondaryTapDown: (details) {
                    _mostrarMenuContextualFila(
                        context, details.globalPosition, fila);
                  },
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.grey[300]!,
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.more_horiz,
                      size: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
