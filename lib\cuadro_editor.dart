import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class CeldaData {
  String contenido = '';
  Color colorFondo = Colors.grey[600]!;
  TextEditingController? controller;

  void initController() {
    controller ??= TextEditingController(text: contenido);
  }

  void dispose() {
    controller?.dispose();
  }
}

class CuadroEditor extends StatefulWidget {
  final int? cuadroId;
  final int? leccionId; // Para crear la relación automáticamente

  const CuadroEditor({
    super.key,
    this.cuadroId,
    this.leccionId,
  });

  @override
  State<CuadroEditor> createState() => _CuadroEditorState();
}

class _CuadroEditorState extends State<CuadroEditor> {
  final TextEditingController _tituloController = TextEditingController();
  int _columnas = 1;
  int _filas = 1;
  bool _isLoading = false;
  bool _isEditing = false;
  Color _colorCuadroGlobal = Colors.grey[600]!; // Color global por defecto
  int? _celdaSeleccionadaFila;
  int? _celdaSeleccionadaColumna;
  int? _columnaSeleccionada; // Para selección de columna completa
  String? _contenidoCopiado; // Para funcionalidad de copiar/pegar

  // Estados para controlar la visibilidad de los selectores con hover
  int? _columnaHover; // Columna sobre la que está el mouse
  int? _filaHover; // Fila sobre la que está el mouse
  Timer? _hoverTimer; // Timer para controlar el delay del hover

  // Matriz para almacenar el contenido de cada celda
  List<List<CeldaData>> _matriz = [];

  // Historial para comandos de teclado (undo/redo)
  List<Map<String, dynamic>> _historial = [];
  int _historialIndex = -1;

  // Anchos de columnas personalizados
  List<double> _anchosColumnas = [];

  // Clipboard para copy/paste
  String? _clipboardContent;

  // Timer para auto-guardar en historial
  Timer? _autoSaveTimer;

  @override
  void initState() {
    super.initState();
    _inicializarMatriz();
    // Seleccionar automáticamente la primera celda
    _celdaSeleccionadaFila = 0;
    _celdaSeleccionadaColumna = 0;
    if (widget.cuadroId != null) {
      _cargarCuadro();
    }
  }

  @override
  void dispose() {
    _hoverTimer?.cancel();
    _autoSaveTimer?.cancel();
    _tituloController.dispose();
    // Limpiar todos los controladores de las celdas
    for (var fila in _matriz) {
      for (var celda in fila) {
        celda.dispose();
      }
    }
    super.dispose();
  }

  void _inicializarMatriz() {
    _matriz = List.generate(_filas, (fila) {
      return List.generate(_columnas, (columna) {
        final celda = CeldaData();
        celda.colorFondo = _colorCuadroGlobal;
        celda.contenido = '';
        celda.initController();
        return celda;
      });
    });

    // Inicializar anchos de columnas con valor por defecto
    _anchosColumnas = List.generate(_columnas, (index) => 200.0);
  }

  // Funciones para comandos de teclado
  void _guardarEstadoEnHistorial() {
    // Limpiar historial futuro si estamos en el medio
    if (_historialIndex < _historial.length - 1) {
      _historial.removeRange(_historialIndex + 1, _historial.length);
    }

    // Agregar estado actual
    _historial.add({
      'matriz': _matriz
          .map((fila) => fila
              .map((celda) => {
                    'contenido': celda.contenido,
                    'colorFondo': celda.colorFondo.value,
                  })
              .toList())
          .toList(),
      'filas': _filas,
      'columnas': _columnas,
      'anchosColumnas': List.from(_anchosColumnas),
    });

    _historialIndex = _historial.length - 1;

    // Limitar historial a 50 estados
    if (_historial.length > 50) {
      _historial.removeAt(0);
      _historialIndex--;
    }
  }

  void _deshacerCambio() {
    if (_historialIndex > 0) {
      _historialIndex--;
      _restaurarEstado(_historial[_historialIndex]);
    }
  }

  void _rehacerCambio() {
    if (_historialIndex < _historial.length - 1) {
      _historialIndex++;
      _restaurarEstado(_historial[_historialIndex]);
    }
  }

  void _restaurarEstado(Map<String, dynamic> estado) {
    setState(() {
      _filas = estado['filas'];
      _columnas = estado['columnas'];
      _anchosColumnas = List<double>.from(estado['anchosColumnas']);

      // Limpiar matriz actual
      for (var fila in _matriz) {
        for (var celda in fila) {
          celda.dispose();
        }
      }

      // Restaurar matriz
      _matriz = List.generate(_filas, (fila) {
        return List.generate(_columnas, (columna) {
          final celda = CeldaData();
          final celdaData = estado['matriz'][fila][columna];
          celda.contenido = celdaData['contenido'];
          celda.colorFondo = Color(celdaData['colorFondo']);
          celda.initController();
          celda.controller!.text = celda.contenido;
          return celda;
        });
      });
    });
  }

  void _copiarCeldaSeleccionada() {
    if (_celdaSeleccionadaFila != null && _celdaSeleccionadaColumna != null) {
      _clipboardContent = _matriz[_celdaSeleccionadaFila!]
              [_celdaSeleccionadaColumna!]
          .contenido;
    }
  }

  void _pegarCeldaSeleccionada() {
    if (_clipboardContent != null &&
        _celdaSeleccionadaFila != null &&
        _celdaSeleccionadaColumna != null) {
      _guardarEstadoEnHistorial();
      setState(() {
        _matriz[_celdaSeleccionadaFila!][_celdaSeleccionadaColumna!].contenido =
            _clipboardContent!;
        _matriz[_celdaSeleccionadaFila!][_celdaSeleccionadaColumna!]
            .controller!
            .text = _clipboardContent!;
      });
    }
  }

  Future<void> _cargarCuadro() async {
    if (widget.cuadroId == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await Supabase.instance.client
          .from('cuadros')
          .select('*')
          .eq('cuadro_id', widget.cuadroId!)
          .single();

      _tituloController.text = response['titulo'] ?? '';

      if (response['json_data'] != null) {
        print('Contenido recibido: ${response['json_data']}');
        print('Tipo de contenido: ${response['json_data'].runtimeType}');

        // Parsear JSON si viene como string, o usar directamente si ya es Map
        Map<String, dynamic> contenidoData;
        try {
          contenidoData = response['json_data'] is String
              ? jsonDecode(response['json_data'] as String)
                  as Map<String, dynamic>
              : response['json_data'] as Map<String, dynamic>;
        } catch (e) {
          print('Error parseando JSON: $e');
          print('Contenido problemático: ${response['json_data']}');
          rethrow;
        }
        _filas = contenidoData['filas'] ?? 2;
        _columnas = contenidoData['columnas'] ?? 2;

        // Cargar color si existe
        if (contenidoData['color'] != null) {
          _colorCuadroGlobal = Color(contenidoData['color'] as int);
        }

        _inicializarMatriz();

        // Cargar anchos de columnas si existen
        if (contenidoData['anchosColumnas'] != null) {
          _anchosColumnas = List<double>.from(contenidoData['anchosColumnas']);
        }

        // Guardar estado inicial en historial
        _guardarEstadoEnHistorial();

        // Manejar formato nuevo (matriz)
        if (contenidoData['matriz'] != null) {
          final matrizData = contenidoData['matriz'] as List;
          for (int fila = 0;
              fila < _filas && fila < matrizData.length;
              fila++) {
            final filaData = matrizData[fila] as List;
            for (int columna = 0;
                columna < _columnas && columna < filaData.length;
                columna++) {
              final celdaData = filaData[columna] as Map<String, dynamic>;

              // Convertir elementos antiguos a contenido simple
              if (celdaData['elementos'] != null) {
                final elementos = celdaData['elementos'] as List<dynamic>;
                List<String> contenidos = [];
                for (var elemento in elementos) {
                  final elementData = elemento as Map<String, dynamic>;
                  final texto = elementData['texto'] ?? '';
                  final tipo = elementData['tipo'] ?? 'texto';
                  if (texto.isNotEmpty) {
                    if (tipo == 'listaDesordenada') {
                      contenidos.add('- $texto');
                    } else {
                      contenidos.add(texto);
                    }
                  }
                }
                _matriz[fila][columna].contenido = contenidos.join('\n');
              } else if (celdaData['contenido'] != null) {
                _matriz[fila][columna].contenido = celdaData['contenido'] ?? '';
              }

              // Cargar color individual de la celda si existe
              if (celdaData['colorFondo'] != null) {
                _matriz[fila][columna].colorFondo =
                    Color(celdaData['colorFondo'] as int);
              }

              // Inicializar controlador con el contenido
              _matriz[fila][columna].initController();
              _matriz[fila][columna].controller!.text =
                  _matriz[fila][columna].contenido;
            }
          }
        }
        // Manejar formato antiguo (celdas)
        else if (contenidoData['celdas'] != null) {
          final celdasData = contenidoData['celdas'] as List;
          for (var celdaInfo in celdasData) {
            final celdaData = celdaInfo as Map<String, dynamic>;
            final fila = celdaData['fila'] as int;
            final columna = celdaData['columna'] as int;
            final titulo = celdaData['titulo'] as String? ?? '';
            final contenido = celdaData['contenido'] as List<dynamic>? ?? [];

            if (fila < _filas && columna < _columnas) {
              List<String> contenidos = [];

              // Agregar título si existe
              if (titulo.isNotEmpty) {
                contenidos.add(titulo);
              }

              // Agregar contenido como viñetas
              for (var item in contenido) {
                if (item.toString().isNotEmpty) {
                  contenidos.add('- ${item.toString()}');
                }
              }

              _matriz[fila][columna].contenido = contenidos.join('\n');
              _matriz[fila][columna].initController();
              _matriz[fila][columna].controller!.text =
                  _matriz[fila][columna].contenido;
            }
          }
        }
      }

      setState(() {
        _isEditing = true;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al cargar cuadro: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _guardarCuadro() async {
    if (_tituloController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('El título es obligatorio')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final contenidoData = {
        'filas': _filas,
        'columnas': _columnas,
        'color': _colorCuadroGlobal.value, // Guardar color como int
        'anchosColumnas': _anchosColumnas, // Guardar anchos personalizados
        'matriz': _matriz.map((fila) {
          return fila.map((celda) {
            // Convertir contenido a elementos para compatibilidad
            List<Map<String, dynamic>> elementos = [];
            if (celda.contenido.isNotEmpty) {
              List<String> lineas = celda.contenido.split('\n');
              for (String linea in lineas) {
                linea = linea.trim();
                if (linea.isNotEmpty) {
                  if (linea.startsWith('- ')) {
                    String texto = linea.substring(2).trim();
                    if (texto.isNotEmpty) {
                      elementos.add({
                        'tipo': 'listaDesordenada',
                        'texto': texto,
                      });
                    }
                  } else {
                    elementos.add({
                      'tipo': 'texto',
                      'texto': linea,
                    });
                  }
                }
              }
            }

            // Si no hay elementos, agregar uno vacío para evitar problemas
            if (elementos.isEmpty) {
              elementos.add({
                'tipo': 'texto',
                'texto': '',
              });
            }

            return {
              'elementos': elementos,
              'contenido': celda.contenido,
              'colorFondo': celda.colorFondo.value,
            };
          }).toList();
        }).toList(),
      };

      if (_isEditing && widget.cuadroId != null) {
        await Supabase.instance.client.from('cuadros').update({
          'titulo': _tituloController.text.trim(),
          'json_data': contenidoData,
        }).eq('cuadro_id', widget.cuadroId!);
      } else {
        final response = await Supabase.instance.client
            .from('cuadros')
            .insert({
              'titulo': _tituloController.text.trim(),
              'json_data': contenidoData,
            })
            .select()
            .single();

        if (widget.leccionId != null) {
          await Supabase.instance.client.from('leccion_cuadros').insert({
            'leccion_id': widget.leccionId!,
            'cuadro_id': response['cuadro_id'],
          });
        }
      }

      if (mounted) {
        Navigator.pop(context, true);
      }
    } catch (e) {
      print('Error al guardar cuadro: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al guardar: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _eliminarFila(int index) {
    if (_filas > 1) {
      setState(() {
        _filas--;
        _matriz.removeAt(index);
      });
    }
  }

  void _eliminarColumna(int index) {
    if (_columnas > 1) {
      setState(() {
        _columnas--;
        for (int fila = 0; fila < _filas; fila++) {
          _matriz[fila].removeAt(index);
        }
        // Ajustar selecciones
        if (_celdaSeleccionadaColumna != null &&
            _celdaSeleccionadaColumna! >= index) {
          _celdaSeleccionadaColumna = _celdaSeleccionadaColumna! > index
              ? _celdaSeleccionadaColumna! - 1
              : null;
        }
        if (_columnaSeleccionada != null && _columnaSeleccionada! >= index) {
          _columnaSeleccionada =
              _columnaSeleccionada! > index ? _columnaSeleccionada! - 1 : null;
        }
      });
    }
  }

  // Funciones del menú contextual
  void _mostrarSelectorColorFila(int fila) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Seleccionar color para fila ${fila + 1}'),
        content: Wrap(
          children: [
            for (Color color in [
              Colors.white,
              Colors.red[100]!,
              Colors.blue[100]!,
              Colors.green[100]!,
              Colors.yellow[100]!,
              Colors.purple[100]!,
              Colors.orange[100]!,
              Colors.grey[100]!,
            ])
              GestureDetector(
                onTap: () {
                  setState(() {
                    for (int col = 0; col < _columnas; col++) {
                      _matriz[fila][col].colorFondo = color;
                    }
                  });
                  Navigator.pop(context);
                },
                child: Container(
                  width: 40,
                  height: 40,
                  margin: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color,
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _insertarFilaArriba(int index) {
    setState(() {
      _filas++;
      _matriz.insert(index, List.generate(_columnas, (columna) => CeldaData()));
      if (_celdaSeleccionadaFila != null && _celdaSeleccionadaFila! >= index) {
        _celdaSeleccionadaFila = _celdaSeleccionadaFila! + 1;
      }
    });
  }

  void _insertarFilaAbajo(int index) {
    setState(() {
      _filas++;
      _matriz.insert(
          index + 1, List.generate(_columnas, (columna) => CeldaData()));
    });
  }

  void _duplicarFila(int index) {
    setState(() {
      _filas++;
      List<CeldaData> filaDuplicada = [];
      for (var celda in _matriz[index]) {
        CeldaData nuevaCelda = CeldaData();
        nuevaCelda.colorFondo = celda.colorFondo;
        nuevaCelda.contenido = celda.contenido;
        nuevaCelda.initController();
        nuevaCelda.controller!.text = nuevaCelda.contenido;
        filaDuplicada.add(nuevaCelda);
      }
      _matriz.insert(index + 1, filaDuplicada);
    });
  }

  void _limpiarContenidoFila(int index) {
    setState(() {
      for (int col = 0; col < _columnas; col++) {
        _matriz[index][col].contenido = '';
        _matriz[index][col].controller?.text = '';
      }
    });
  }

  // Funciones del menú contextual de columna
  void _mostrarSelectorColorColumna(int columna) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Seleccionar color para columna ${columna + 1}'),
        content: Wrap(
          children: [
            for (Color color in [
              Colors.white,
              Colors.red[100]!,
              Colors.blue[100]!,
              Colors.green[100]!,
              Colors.yellow[100]!,
              Colors.purple[100]!,
              Colors.orange[100]!,
              Colors.grey[100]!,
            ])
              GestureDetector(
                onTap: () {
                  setState(() {
                    for (int fila = 0; fila < _filas; fila++) {
                      _matriz[fila][columna].colorFondo = color;
                    }
                  });
                  Navigator.pop(context);
                },
                child: Container(
                  width: 40,
                  height: 40,
                  margin: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color,
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _insertarColumnaIzquierda(int index) {
    _guardarEstadoEnHistorial();
    setState(() {
      _columnas++;
      for (int fila = 0; fila < _filas; fila++) {
        CeldaData celda = CeldaData();
        celda.colorFondo = _colorCuadroGlobal;
        celda.initController();
        _matriz[fila].insert(index, celda);
      }
      // Insertar ancho de columna por defecto
      _anchosColumnas.insert(index, 200.0);

      // Ajustar selección
      if (_celdaSeleccionadaColumna != null &&
          _celdaSeleccionadaColumna! >= index) {
        _celdaSeleccionadaColumna = _celdaSeleccionadaColumna! + 1;
      }
      if (_columnaSeleccionada != null && _columnaSeleccionada! >= index) {
        _columnaSeleccionada = _columnaSeleccionada! + 1;
      }
    });
  }

  void _insertarColumnaDerecha(int index) {
    _guardarEstadoEnHistorial();
    setState(() {
      _columnas++;
      for (int fila = 0; fila < _filas; fila++) {
        CeldaData celda = CeldaData();
        celda.colorFondo = _colorCuadroGlobal;
        celda.initController();
        _matriz[fila].insert(index + 1, celda);
      }
      // Insertar ancho de columna por defecto
      _anchosColumnas.insert(index + 1, 200.0);
    });
  }

  void _duplicarColumna(int index) {
    setState(() {
      _columnas++;
      for (int fila = 0; fila < _filas; fila++) {
        CeldaData celdaOriginal = _matriz[fila][index];
        CeldaData nuevaCelda = CeldaData();
        nuevaCelda.colorFondo = celdaOriginal.colorFondo;
        nuevaCelda.contenido = celdaOriginal.contenido;
        nuevaCelda.initController();
        nuevaCelda.controller!.text = nuevaCelda.contenido;
        _matriz[fila].insert(index + 1, nuevaCelda);
      }
    });
  }

  void _limpiarContenidoColumna(int index) {
    setState(() {
      for (int fila = 0; fila < _filas; fila++) {
        _matriz[fila][index].contenido = '';
        _matriz[fila][index].controller?.text = '';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      focusNode: FocusNode(),
      autofocus: true,
      onKeyEvent: (KeyEvent event) {
        if (event is KeyDownEvent) {
          final isCtrlPressed = HardwareKeyboard.instance.isControlPressed;
          final key = event.logicalKey;

          if (isCtrlPressed) {
            if (key == LogicalKeyboardKey.keyZ) {
              _deshacerCambio();
            } else if (key == LogicalKeyboardKey.keyY) {
              _rehacerCambio();
            } else if (key == LogicalKeyboardKey.keyC) {
              _copiarCeldaSeleccionada();
            } else if (key == LogicalKeyboardKey.keyV) {
              _pegarCeldaSeleccionada();
            }
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(_isEditing ? 'Editar Cuadro' : 'Nuevo Cuadro'),
          actions: [
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              IconButton(
                icon: const Icon(Icons.save),
                onPressed: _guardarCuadro,
                tooltip: 'Guardar cuadro',
              ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildEditor(),
      ),
    );
  }

  Widget _buildEditor() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Campo título
          TextField(
            controller: _tituloController,
            decoration: const InputDecoration(
              labelText: 'Título del cuadro',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),

          // Selector de color
          _buildColorSelector(),
          const SizedBox(height: 16),

          // Editor de tabla
          Expanded(child: _buildTableEditor()),
        ],
      ),
    );
  }

  Widget _buildColorSelector() {
    final colores = [
      Colors.grey[600]!,
      Colors.blue[600]!,
      Colors.green[600]!,
      Colors.red[600]!,
      Colors.orange[600]!,
      Colors.purple[600]!,
      Colors.teal[600]!,
      Colors.brown[600]!,
    ];

    return Row(
      children: [
        const Text('Color: '),
        const SizedBox(width: 8),
        ...colores.map((color) {
          return GestureDetector(
            onTap: () {
              setState(() {
                if (_celdaSeleccionadaFila != null &&
                    _celdaSeleccionadaColumna != null) {
                  // Cambiar color de celda específica
                  _matriz[_celdaSeleccionadaFila!][_celdaSeleccionadaColumna!]
                      .colorFondo = color;
                } else {
                  // Cambiar color global por defecto
                  _colorCuadroGlobal = color;
                  // Aplicar a todas las celdas
                  for (var fila in _matriz) {
                    for (var celda in fila) {
                      celda.colorFondo = color;
                    }
                  }
                }
              });
            },
            child: Container(
              margin: const EdgeInsets.only(right: 8),
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: color,
                border: Border.all(
                  color: _colorCuadroGlobal == color
                      ? Colors.black
                      : Colors.transparent,
                  width: 3,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          );
        }),
        const SizedBox(width: 16),
        Text(
          _celdaSeleccionadaFila != null && _celdaSeleccionadaColumna != null
              ? 'Celda: ${_celdaSeleccionadaFila! + 1},${_celdaSeleccionadaColumna! + 1}'
              : 'Celda: 1,1',
          style: TextStyle(
            fontSize: 12,
            color: Colors.blue[600],
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildTableEditor() {
    return SingleChildScrollView(
      child: Center(
        child: Container(
          margin: EdgeInsets.only(top: 35, left: 35), // Espacio para selectores
          child: Stack(
            clipBehavior:
                Clip.none, // Permitir que los selectores salgan del Stack
            children: [
              // Tabla de datos
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  for (int fila = 0; fila < _filas; fila++)
                    IntrinsicHeight(
                      child: Stack(
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              for (int columna = 0;
                                  columna < _columnas;
                                  columna++)
                                _buildCelda(fila, columna),
                            ],
                          ),
                          // Botón flotante fijo en la primera línea de esta fila
                          _buildSelectorFilaIntegrado(fila),
                        ],
                      ),
                    ),
                ],
              ),
              // Selectores flotantes de columna
              for (int columna = 0; columna < _columnas; columna++)
                _buildSelectorColumnaFlotante(columna),
            ],
          ),
        ),
      ),
    );
  }

  // Funciones del menú contextual de fila (simplificada para selectores flotantes)
  void _mostrarMenuContextualFila(
      BuildContext context, Offset position, int fila) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy,
        position.dx + 1,
        position.dy + 1,
      ),
      items: [
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.palette, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Color'),
            ],
          ),
          onTap: () => _mostrarSelectorColorFila(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.keyboard_arrow_up, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Insert Above'),
            ],
          ),
          onTap: () => _insertarFilaArriba(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.keyboard_arrow_down,
                  size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Insert Below'),
            ],
          ),
          onTap: () => _insertarFilaAbajo(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.content_copy, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Duplicate'),
            ],
          ),
          onTap: () => _duplicarFila(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.clear, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Clear Contents'),
            ],
          ),
          onTap: () => _limpiarContenidoFila(fila),
        ),
        if (_filas > 1)
          PopupMenuItem(
            child: Row(
              children: [
                Icon(Icons.delete, size: 16, color: Colors.red[600]),
                SizedBox(width: 8),
                Text('Delete', style: TextStyle(color: Colors.red[600])),
              ],
            ),
            onTap: () => _eliminarFila(fila),
          ),
      ],
    );
  }

  // Mostrar menú contextual para operaciones de columna
  void _mostrarMenuContextualColumna(
      BuildContext context, Offset position, int columna) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy,
        position.dx + 1,
        position.dy + 1,
      ),
      items: [
        PopupMenuItem(
          enabled: false,
          child: SizedBox(
            width: 200,
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search actions...',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              style: TextStyle(fontSize: 14),
            ),
          ),
        ),
        PopupMenuItem(
          enabled: false,
          child: Text('Table',
              style: TextStyle(
                  fontWeight: FontWeight.bold, color: Colors.grey[600])),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.palette, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Color'),
              Spacer(),
              Icon(Icons.chevron_right, size: 16, color: Colors.grey[400]),
            ],
          ),
          onTap: () => _mostrarSelectorColorColumna(columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.keyboard_arrow_left,
                  size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Insert Left'),
            ],
          ),
          onTap: () => _insertarColumnaIzquierda(columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.keyboard_arrow_right,
                  size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Insert Right'),
            ],
          ),
          onTap: () => _insertarColumnaDerecha(columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.content_copy, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Duplicate'),
              Spacer(),
              Text('Ctrl+D',
                  style: TextStyle(color: Colors.grey[400], fontSize: 12)),
            ],
          ),
          onTap: () => _duplicarColumna(columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.clear, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Clear Contents'),
            ],
          ),
          onTap: () => _limpiarContenidoColumna(columna),
        ),
        if (_columnas > 1)
          PopupMenuItem(
            child: Row(
              children: [
                Icon(Icons.delete, size: 16, color: Colors.red[600]),
                SizedBox(width: 8),
                Text('Delete', style: TextStyle(color: Colors.red[600])),
                Spacer(),
                Text('Del',
                    style: TextStyle(color: Colors.grey[400], fontSize: 12)),
              ],
            ),
            onTap: () => _eliminarColumna(columna),
          ),
      ],
    );
  }

  // Mostrar menú contextual para operaciones de celda individual
  void _mostrarMenuContextualCelda(
      BuildContext context, Offset position, int fila, int columna) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy,
        position.dx + 1,
        position.dy + 1,
      ),
      items: [
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.content_copy, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Copy'),
              Spacer(),
              Text('Ctrl+C',
                  style: TextStyle(color: Colors.grey[400], fontSize: 12)),
            ],
          ),
          onTap: () => _copiarCelda(fila, columna),
        ),
        if (_contenidoCopiado != null)
          PopupMenuItem(
            child: Row(
              children: [
                Icon(Icons.content_paste, size: 16, color: Colors.grey[600]),
                SizedBox(width: 8),
                Text('Paste'),
                Spacer(),
                Text('Ctrl+V',
                    style: TextStyle(color: Colors.grey[400], fontSize: 12)),
              ],
            ),
            onTap: () => _pegarCelda(fila, columna),
          ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.clear, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Clear Contents'),
            ],
          ),
          onTap: () => _limpiarCelda(fila, columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.palette, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Color'),
              Spacer(),
              Icon(Icons.chevron_right, size: 16, color: Colors.grey[400]),
            ],
          ),
          onTap: () => _mostrarSelectorColorCelda(fila, columna),
        ),
      ],
    );
  }

  // Funciones para operaciones de celda individual
  void _copiarCelda(int fila, int columna) {
    final celda = _matriz[fila][columna];
    setState(() {
      _contenidoCopiado = celda.contenido;
    });
  }

  void _pegarCelda(int fila, int columna) {
    if (_contenidoCopiado != null) {
      setState(() {
        _matriz[fila][columna].contenido = _contenidoCopiado!;
        _matriz[fila][columna].controller?.text = _contenidoCopiado!;
      });
    }
  }

  void _limpiarCelda(int fila, int columna) {
    setState(() {
      _matriz[fila][columna].contenido = '';
      _matriz[fila][columna].controller?.text = '';
    });
  }

  void _mostrarSelectorColorCelda(int fila, int columna) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Seleccionar color para celda'),
        content: Wrap(
          children: [
            for (Color color in [
              Colors.white,
              Colors.red[100]!,
              Colors.blue[100]!,
              Colors.green[100]!,
              Colors.yellow[100]!,
              Colors.purple[100]!,
              Colors.orange[100]!,
              Colors.grey[100]!,
            ])
              GestureDetector(
                onTap: () {
                  setState(() {
                    _matriz[fila][columna].colorFondo = color;
                  });
                  Navigator.pop(context);
                },
                child: Container(
                  width: 40,
                  height: 40,
                  margin: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color,
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCelda(int fila, int columna) {
    final celda = _matriz[fila][columna];
    final isSelected =
        _celdaSeleccionadaFila == fila && _celdaSeleccionadaColumna == columna;

    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _columnaHover = columna;
          _filaHover = fila;
        });
      },
      onExit: (_) {
        // Cancelar timer anterior si existe
        _hoverTimer?.cancel();
        // Usar un delay más largo para dar tiempo a alcanzar el botón
        _hoverTimer = Timer(Duration(milliseconds: 300), () {
          if (mounted) {
            setState(() {
              _columnaHover = null;
              _filaHover = null;
            });
          }
        });
      },
      child: GestureDetector(
          onTap: () {
            setState(() {
              _celdaSeleccionadaFila = fila;
              _celdaSeleccionadaColumna = columna;
              // Limpiar selección de columna
              _columnaSeleccionada = null;
            });
          },
          onSecondaryTapDown: (details) {
            setState(() {
              _celdaSeleccionadaFila = fila;
              _celdaSeleccionadaColumna = columna;
              _columnaSeleccionada = null;
            });

            // Detectar si el click fue cerca del borde izquierdo (área de fila)
            RenderBox renderBox = context.findRenderObject() as RenderBox;
            Offset localPosition =
                renderBox.globalToLocal(details.globalPosition);

            if (localPosition.dx < 30) {
              // Click en área de fila - mostrar menú de fila
              _mostrarMenuContextualFila(context, details.globalPosition, fila);
            } else {
              // Click en celda - mostrar menú de celda
              _mostrarMenuContextualCelda(
                  context, details.globalPosition, fila, columna);
            }
          },
          child: Row(
            children: [
              Container(
                width: _anchosColumnas[columna], // Ancho personalizable
                height: double
                    .infinity, // Usar toda la altura disponible de la fila
                alignment:
                    Alignment.topLeft, // Alinear contenido arriba-izquierda
                decoration: BoxDecoration(
                  color: celda.colorFondo
                      .withValues(alpha: 0.1), // Fondo del cuadro individual
                  border: Border(
                    top: BorderSide(
                        color: isSelected ? Colors.blue : Colors.grey[300]!,
                        width: isSelected ? 2.0 : 1.0),
                    left: BorderSide(
                        color: isSelected ? Colors.blue : Colors.grey[300]!,
                        width: isSelected ? 2.0 : 1.0),
                    right: BorderSide(
                        color: isSelected ? Colors.blue : Colors.grey[300]!,
                        width: isSelected ? 2.0 : 1.0),
                    bottom: BorderSide(
                        color: isSelected ? Colors.blue : Colors.grey[300]!,
                        width: isSelected ? 2.0 : 1.0),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(6.0),
                  child: TextField(
                    controller: celda.controller,
                    maxLines: null,
                    minLines: 1,
                    keyboardType: TextInputType.multiline,
                    textInputAction: TextInputAction.newline,
                    decoration: InputDecoration(
                      hintText: '',
                      border: InputBorder.none,
                      isDense: true,
                    ),
                    style: TextStyle(
                      fontSize: 14,
                      height: 1.3,
                    ),
                    onChanged: (value) {
                      celda.contenido = value;

                      // Cancelar timer anterior si existe
                      _autoSaveTimer?.cancel();

                      // Crear nuevo timer para guardar estado después de 2 segundos de inactividad
                      _autoSaveTimer = Timer(Duration(seconds: 2), () {
                        _guardarEstadoEnHistorial();
                      });
                    },
                  ),
                ),
              ),
              // Divisor redimensionable (para todas las columnas)
              if (true) // Siempre mostrar divisor
                GestureDetector(
                  onPanUpdate: (details) {
                    setState(() {
                      _anchosColumnas[columna] =
                          (_anchosColumnas[columna] + details.delta.dx)
                              .clamp(50.0, 500.0);
                    });
                  },
                  child: MouseRegion(
                    cursor: SystemMouseCursors.resizeColumn,
                    child: Container(
                      width: 8,
                      height: double.infinity,
                      color: Colors.transparent,
                      child: Center(
                        child: Container(
                          width: 2,
                          height: double.infinity,
                          color: Colors.grey[400],
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          )),
    );
  }

  // Selector flotante de columna que aparece con hover
  Widget _buildSelectorColumnaFlotante(int columna) {
    final isSelected = _columnaSeleccionada == columna;
    final isVisible = _columnaHover == columna;

    // Calcular posición basada en anchos acumulados
    double leftPosition = 0;
    for (int i = 0; i < columna; i++) {
      leftPosition += _anchosColumnas[i];
    }
    leftPosition += _anchosColumnas[columna] / 2 -
        12; // Centro de la columna menos la mitad del botón

    return Positioned(
      left: leftPosition,
      top: -12, // Superpuesto con la línea superior de la celda
      child: MouseRegion(
        onEnter: (_) {
          // Cancelar timer de ocultación si existe
          _hoverTimer?.cancel();
          setState(() {
            _columnaHover = columna;
          });
        },
        child: AnimatedOpacity(
          opacity: isVisible ? 1.0 : 0.0,
          duration: Duration(milliseconds: 200),
          child: SizedBox(
            // Área de hover expandida
            width: 40,
            height: 40,
            child: Center(
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(12),
                child: GestureDetector(
                  onTapDown: (details) {
                    setState(() {
                      _columnaSeleccionada = columna;
                      _celdaSeleccionadaFila = null;
                      _celdaSeleccionadaColumna = null;
                    });
                    // Mostrar menú contextual con click izquierdo usando la posición real
                    _mostrarMenuContextualColumna(
                        context, details.globalPosition, columna);
                  },
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.blue[100] : Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected ? Colors.blue : Colors.grey[300]!,
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.more_horiz,
                      size: 12,
                      color: isSelected ? Colors.blue[700] : Colors.grey[600],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Selector flotante de fila integrado - fijo en primera línea
  Widget _buildSelectorFilaIntegrado(int fila) {
    final isVisible = _filaHover == fila;

    return Positioned(
      left: -12, // Superpuesto con la línea izquierda de la celda
      top: 6, // Fijo en la primera línea de texto de esta fila
      child: MouseRegion(
        onEnter: (_) {
          _hoverTimer?.cancel();
          setState(() {
            _filaHover = fila;
          });
        },
        onExit: (_) {
          _hoverTimer?.cancel();
          _hoverTimer = Timer(Duration(milliseconds: 300), () {
            if (mounted) {
              setState(() {
                _filaHover = null;
              });
            }
          });
        },
        child: AnimatedOpacity(
          opacity: isVisible ? 1.0 : 0.0,
          duration: Duration(milliseconds: 200),
          child: SizedBox(
            width: 40,
            height: 40,
            child: Center(
              child: GestureDetector(
                onTapDown: (TapDownDetails details) {
                  _mostrarMenuContextualFila(
                    context,
                    details.globalPosition,
                    fila,
                  );
                },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.blue[600],
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.more_horiz,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
