import 'dart:io';
import 'dart:convert';

void main() async {
  print('🔄 Procesando diccionario Hunspell...');

  // Detectar si estamos en el directorio scripts o en el directorio raíz
  final currentDir = Directory.current.path;
  final isInScriptsDir = currentDir.endsWith('scripts');
  final assetsPath = isInScriptsDir ? '../assets' : 'assets';

  // Leer archivo .aff primero
  final archivoAff = File('$assetsPath/es_words.aff');
  if (!await archivoAff.exists()) {
    print('❌ Error: No se encuentra el archivo es_words.aff en assets/');
    return;
  }

  // Leer archivo .dic
  final archivo = File('$assetsPath/es_words.dic');
  if (!await archivo.exists()) {
    print('❌ Error: No se encuentra el archivo es_words.dic en assets/');
    return;
  }

  // Procesar reglas del .aff
  print('📖 Leyendo reglas del archivo .aff...');
  final reglasAff = await procesarArchivoAff(archivoAff);
  print('✅ Cargadas ${reglasAff.length} reglas del .aff');

  // Debug: mostrar algunas reglas importantes
  if (reglasAff.containsKey('s')) {
    print('📋 Reglas para "s": ${reglasAff['s']!.take(3).toList()}');
  }

  // Intentar diferentes codificaciones
  String contenido;
  try {
    // Primero intentar UTF-8
    contenido = await archivo.readAsString();
    print('✅ Archivo leído como UTF-8');
  } catch (e) {
    try {
      // Si falla, intentar Latin-1
      final bytes = await archivo.readAsBytes();
      contenido = latin1.decode(bytes);
      print('✅ Archivo leído como Latin-1');
    } catch (e2) {
      print('❌ Error: No se pudo leer el archivo con ninguna codificación');
      print('   UTF-8 error: $e');
      print('   Latin-1 error: $e2');
      return;
    }
  }
  final lineas = contenido.split('\n');

  print('📊 Total líneas en .dic: ${lineas.length}');

  // Primero: analizar las reglas más comunes
  final Map<String, int> conteoReglas = {};
  final Map<String, List<String>> ejemplosReglas = {};

  print('🔍 Analizando reglas del diccionario...');

  for (final linea in lineas) {
    final lineaLimpia = linea.trim().toLowerCase();
    if (lineaLimpia.isEmpty ||
        lineaLimpia.startsWith('#') ||
        !lineaLimpia.contains('/')) continue;

    final partes = lineaLimpia.split('/');
    if (partes.length < 2) continue;

    final palabraBase = partes[0];
    final reglas = partes[1];

    // Contar cada regla
    for (int i = 0; i < reglas.length; i++) {
      final regla = reglas[i];
      conteoReglas[regla] = (conteoReglas[regla] ?? 0) + 1;

      // Guardar ejemplos
      if (!ejemplosReglas.containsKey(regla)) {
        ejemplosReglas[regla] = [];
      }
      if (ejemplosReglas[regla]!.length < 5) {
        ejemplosReglas[regla]!.add('$palabraBase/$reglas');
      }
    }
  }

  // Mostrar las reglas más comunes
  final reglasOrdenadas = conteoReglas.entries.toList()
    ..sort((a, b) => b.value.compareTo(a.value));

  print('📊 Top 10 reglas más comunes:');
  for (int i = 0; i < 10 && i < reglasOrdenadas.length; i++) {
    final regla = reglasOrdenadas[i].key;
    final count = reglasOrdenadas[i].value;
    final ejemplos = ejemplosReglas[regla]!.take(3).join(', ');
    print('   $regla: $count veces - Ejemplos: $ejemplos');
  }

  // Procesar y expandir palabras
  final Set<String> palabrasExpandidas = {};
  int lineasProcesadas = 0;
  int palabrasConReglas = 0;
  int palabrasSimples = 0;

  for (final linea in lineas) {
    final lineaLimpia = linea.trim().toLowerCase();
    if (lineaLimpia.isEmpty || lineaLimpia.startsWith('#')) continue;

    lineasProcesadas++;

    if (lineaLimpia.contains('/')) {
      // Formato Hunspell: "animal/S"
      final partes = lineaLimpia.split('/');
      final palabraBase = partes[0];
      final reglas = partes.length > 1 ? partes[1] : '';

      palabrasConReglas++;

      // Agregar palabra base
      palabrasExpandidas.add(palabraBase);

      // Expandir según reglas reales del .aff
      final formasGeneradas =
          expandirPalabraConAff(palabraBase, reglas, reglasAff);
      palabrasExpandidas.addAll(formasGeneradas);

      // Debug para algunas palabras
      if (['común', 'animal', 'médico', 'evaluación'].contains(palabraBase)) {
        final formas = expandirPalabraConAff(palabraBase, reglas, reglasAff);
        print('🔍 "$palabraBase/$reglas" → $formas');

        // Debug de las reglas aplicadas
        if (palabraBase == 'común') {
          print('   - Reglas para "s": ${reglasAff['s']?.take(3).toList()}');
          print('   - Reglas para "S": ${reglasAff['S']?.take(3).toList()}');
          print(
              '   - Códigos disponibles: ${reglasAff.keys.take(10).toList()}');
        }
      }

      // Buscar palabras que contengan "evaluación"
      if (palabraBase.contains('evaluaci')) {
        final formas = expandirPalabraConAff(palabraBase, reglas, reglasAff);
        print('🔍 EVALUACIÓN: "$palabraBase/$reglas" → $formas');
      }
    } else {
      // Palabra simple sin reglas
      palabrasExpandidas.add(lineaLimpia);
      palabrasSimples++;
    }
  }

  print('📈 Estadísticas procesamiento:');
  print('   - Líneas procesadas: $lineasProcesadas');
  print('   - Palabras con reglas: $palabrasConReglas');
  print('   - Palabras simples: $palabrasSimples');
  print('   - Total palabras expandidas: ${palabrasExpandidas.length}');

  // Agregar verbos conjugados si existe el archivo
  final archivoVerbos = File('../assets/es_conj.txt');
  if (await archivoVerbos.exists()) {
    print('\n📖 Agregando verbos conjugados...');

    try {
      String contenidoVerbos;
      try {
        contenidoVerbos = await archivoVerbos.readAsString();
      } catch (e) {
        final bytes = await archivoVerbos.readAsBytes();
        contenidoVerbos = latin1.decode(bytes);
      }

      final verbosConjugados = contenidoVerbos
          .split('\n')
          .map((verbo) => verbo.trim().toLowerCase())
          .where((verbo) => verbo.isNotEmpty && !verbo.startsWith('#'))
          .toSet();

      final palabrasAntesVerbos = palabrasExpandidas.length;
      palabrasExpandidas.addAll(verbosConjugados);
      final verbosAgregados = palabrasExpandidas.length - palabrasAntesVerbos;

      print('✅ Verbos procesados: ${verbosConjugados.length}');
      print('📊 Nuevos agregados: $verbosAgregados');
    } catch (e) {
      print('❌ Error cargando verbos conjugados: $e');
    }
  } else {
    print(
        '⚠️ Archivo es_conj.txt no encontrado, continuando sin verbos conjugados');
  }

  // PASO 3: Normalización y filtrado final
  print('\n🔧 PASO 3: Normalizando y filtrando...');
  final palabrasAntesLimpieza = palabrasExpandidas.length;

  // Aplicar normalización y filtrado a todas las palabras
  final palabrasLimpias = palabrasExpandidas
      .map((palabra) => _normalizarPalabra(palabra))
      .where((palabra) => _esValidaPalabra(palabra))
      .toSet();

  final palabrasFiltradas = palabrasAntesLimpieza - palabrasLimpias.length;

  // PASO 4: Ordenamiento alfabético
  print('🔤 PASO 4: Ordenando alfabéticamente...');
  final palabrasFinales = palabrasLimpias.toList()..sort();

  print('✅ Palabras filtradas: $palabrasFiltradas');
  print('📊 Total final: ${palabrasFinales.length}');

  // PASO 5: Crear JSON optimizado
  print('\n💾 PASO 5: Generando JSON final...');
  final diccionarioJson = {
    'version': 'diccionario_completo_v1',
    'fuentes': ['Hunspell Español Expandido', 'Verbos Conjugados'],
    'fecha_creacion': DateTime.now().toIso8601String(),
    'palabras': palabrasFinales, // Ya ordenadas alfabéticamente
    'total_palabras': palabrasFinales.length,
    'estadisticas': {
      'hunspell_expandido': lineasProcesadas,
      'palabras_con_reglas': palabrasConReglas,
      'palabras_simples': palabrasSimples,
      'verbos_agregados':
          palabrasFinales.length - (palabrasAntesLimpieza - palabrasFiltradas),
      'palabras_filtradas': palabrasFiltradas,
      'total_fuentes': palabrasAntesLimpieza,
      'total_final': palabrasFinales.length,
    }
  };

  // Guardar JSON
  final archivoSalida = File('../assets/diccionario_expandido.json');
  await archivoSalida.writeAsString(json.encode(diccionarioJson));

  print('✅ Diccionario expandido guardado en: ${archivoSalida.path}');
  print('📊 Total palabras finales: ${palabrasFinales.length}');

  // PASO 6: Verificación final
  print('\n🧪 PASO 6: Verificación de palabras clave:');
  final palabrasPrueba = [
    'animal',
    'animales',
    'común',
    'comunes',
    'médico',
    'médica',
    'evaluación',
    'evaluaciones',
    'demuestran',
    'desarrollan',
    'establecen',
    'realizan',
    'presentan',
    'consideran',
    'analizan',
    'estudian'
  ];

  int palabrasEncontradas = 0;
  for (final palabra in palabrasPrueba) {
    final existe = palabrasFinales.contains(palabra);
    if (existe) palabrasEncontradas++;
    print('   - "$palabra": ${existe ? "✅" : "❌"}');
  }

  print('\n📈 RESUMEN FINAL:');
  print(
      '   🔤 Palabras verificadas: $palabrasEncontradas/${palabrasPrueba.length}');
  print('   📊 Total diccionario: ${palabrasFinales.length} palabras');
  print('   🎯 Ordenamiento: Alfabético');
  print('   ✅ Proceso completado exitosamente');
}

// Expandir palabra usando reglas reales del .aff
List<String> expandirPalabraConAff(String palabraBase, String codigosReglas,
    Map<String, List<ReglaAff>> reglasAff) {
  final formas = <String>[];

  for (int i = 0; i < codigosReglas.length; i++) {
    final codigo = codigosReglas[i];

    // Intentar tanto minúscula como mayúscula
    final codigoMayuscula = codigo.toUpperCase();
    final codigoMinuscula = codigo.toLowerCase();

    List<ReglaAff>? reglasParaCodigo;
    if (reglasAff.containsKey(codigo)) {
      reglasParaCodigo = reglasAff[codigo];
    } else if (reglasAff.containsKey(codigoMayuscula)) {
      reglasParaCodigo = reglasAff[codigoMayuscula];
    } else if (reglasAff.containsKey(codigoMinuscula)) {
      reglasParaCodigo = reglasAff[codigoMinuscula];
    }

    if (reglasParaCodigo != null) {
      final reglas = reglasParaCodigo;

      for (final regla in reglas) {
        // Verificar si la palabra cumple la condición
        if (_cumpleCondicion(palabraBase, regla.condicion)) {
          // Aplicar la regla: quitar + agregar
          String nuevaForma = palabraBase;

          // Quitar sufijo si es necesario
          if (regla.quitar.isNotEmpty && palabraBase.endsWith(regla.quitar)) {
            nuevaForma = palabraBase.substring(
                0, palabraBase.length - regla.quitar.length);
          }

          // Agregar nuevo sufijo
          nuevaForma += regla.agregar;

          if (nuevaForma != palabraBase) {
            formas.add(nuevaForma);
          }
        }
      }
    }
  }

  return formas;
}

// Verificar si una palabra cumple una condición del .aff
bool _cumpleCondicion(String palabra, String condicion) {
  if (condicion == '.') return true; // Cualquier palabra

  // Condiciones comunes de Hunspell
  if (condicion.startsWith('[') && condicion.endsWith(']')) {
    // [aeiou] = termina en vocal
    final caracteres = condicion.substring(1, condicion.length - 1);
    return caracteres.contains(palabra[palabra.length - 1]);
  }

  if (condicion.startsWith('[^') && condicion.endsWith(']')) {
    // [^aeiou] = NO termina en vocal
    final caracteres = condicion.substring(2, condicion.length - 1);
    return !caracteres.contains(palabra[palabra.length - 1]);
  }

  // Condición literal: debe terminar en esa cadena
  return palabra.endsWith(condicion);
}

// Expandir palabra según reglas de Hunspell (versión antigua)
List<String> expandirPalabraHunspell(String palabraBase, String reglas) {
  final formas = <String>[];

  for (int i = 0; i < reglas.length; i++) {
    final regla = reglas[i];

    switch (regla) {
      case 's': // Plural simple: agregar "s" o "es"
      case 'S':
        // Palabras que terminan en consonante (incluyendo ñ, n con acento)
        if (palabraBase.endsWith('n') ||
            palabraBase.endsWith('ñ') ||
            palabraBase.endsWith('r') ||
            palabraBase.endsWith('l') ||
            palabraBase.endsWith('d') ||
            palabraBase.endsWith('z') ||
            // Consonantes con acento
            RegExp(r'[nrldzñ]$').hasMatch(palabraBase) ||
            // Palabras que terminan en vocal con acento + n
            RegExp(r'[áéíóú]n$').hasMatch(palabraBase)) {
          formas.add('${palabraBase}es'); // común → comunes
        } else {
          formas.add('${palabraBase}s'); // casa → casas
        }
        break;

      case 'e': // Plural con "es"
      case 'E': // Plural con "es"
        formas.add('${palabraBase}es');
        break;

      case 'm': // Género: cambiar "o" por "a"
      case 'M': // Género: cambiar "o" por "a"
        if (palabraBase.endsWith('o')) {
          final raiz = palabraBase.substring(0, palabraBase.length - 1);
          formas.add('${raiz}a'); // médico → médica
        }
        break;

      case 'g': // Género femenino (común en español)
        if (palabraBase.endsWith('o')) {
          final raiz = palabraBase.substring(0, palabraBase.length - 1);
          formas.add('${raiz}a'); // médico → médica
        }
        break;

      case 'k': // Plural especial
        if (palabraBase.endsWith('z')) {
          final raiz = palabraBase.substring(0, palabraBase.length - 1);
          formas.add('${raiz}ces'); // luz → luces
        } else if (!palabraBase.endsWith('s')) {
          formas.add('${palabraBase}es'); // animal → animales
        }
        break;

      case 'n': // Plural especial para palabras terminadas en consonante
      case 'N':
        if (!palabraBase.endsWith('s') && !palabraBase.endsWith('n')) {
          formas.add('${palabraBase}es');
        }
        break;

      case 'z': // Cambio z → ces
      case 'Z':
        if (palabraBase.endsWith('z')) {
          final raiz = palabraBase.substring(0, palabraBase.length - 1);
          formas.add('${raiz}ces'); // luz → luces
        }
        break;
    }
  }

  // Para combinaciones comunes
  if (reglas.contains('s') && reglas.contains('g')) {
    // sg = plural + género
    if (palabraBase.endsWith('o')) {
      final raiz = palabraBase.substring(0, palabraBase.length - 1);
      formas.add('${raiz}os'); // médico → médicos
      formas.add('${raiz}as'); // médico → médicas
    }
  }

  if (reglas.contains('s') && reglas.contains('k')) {
    // sk = plural especial
    if (!palabraBase.endsWith('s')) {
      formas.add('${palabraBase}es'); // animal → animales
    }
  }

  return formas;
}

// Procesar archivo .aff para extraer reglas
Future<Map<String, List<ReglaAff>>> procesarArchivoAff(File archivo) async {
  final Map<String, List<ReglaAff>> reglas = {};

  try {
    String contenido;
    try {
      contenido = await archivo.readAsString();
    } catch (e) {
      final bytes = await archivo.readAsBytes();
      contenido = latin1.decode(bytes);
    }

    final lineas = contenido.split('\n');

    print('📊 Total líneas en .aff: ${lineas.length}');

    int lineasSFX = 0;

    for (final linea in lineas) {
      final lineaLimpia = linea.trim();
      if (lineaLimpia.isEmpty || lineaLimpia.startsWith('#')) continue;

      // Debug: mostrar primeras líneas
      if (lineasSFX < 10 && lineaLimpia.startsWith('SFX')) {
        print('🔍 Línea SFX: "$lineaLimpia"');
      }

      // Buscar reglas SFX (sufijos)
      if (lineaLimpia.startsWith('SFX ')) {
        lineasSFX++;
        final partes = lineaLimpia.split(RegExp(r'\s+'));
        if (partes.length >= 5) {
          final codigo = partes[1]; // s, k, g, etc.
          final quitar = partes[2] == '0' ? '' : partes[2];
          final agregar = partes[3] == '0' ? '' : partes[3];
          final condicion = partes[4];

          if (!reglas.containsKey(codigo)) {
            reglas[codigo] = [];
          }

          reglas[codigo]!.add(ReglaAff(
            quitar: quitar,
            agregar: agregar,
            condicion: condicion,
          ));
        }
      }
    }

    print('📈 Estadísticas .aff:');
    print('   - Líneas SFX encontradas: $lineasSFX');
    print('   - Códigos de reglas únicos: ${reglas.keys.length}');
    print('   - Códigos: ${reglas.keys.take(10).toList()}');

    return reglas;
  } catch (e) {
    print('❌ Error procesando .aff: $e');
    return {};
  }
}

// Clase para representar una regla del .aff
class ReglaAff {
  final String quitar;
  final String agregar;
  final String condicion;

  ReglaAff({
    required this.quitar,
    required this.agregar,
    required this.condicion,
  });

  @override
  String toString() => 'quitar:"$quitar" + agregar:"$agregar" si "$condicion"';
}

// Normalizar palabra: minúsculas, sin espacios, caracteres válidos
String _normalizarPalabra(String palabra) {
  return palabra.trim().toLowerCase().replaceAll(
      RegExp(r'[^\wáéíóúüñç]'), ''); // Solo letras válidas del español
}

// Validar si una palabra es válida para el diccionario
bool _esValidaPalabra(String palabra) {
  if (palabra.isEmpty) return false;
  if (palabra.length < 2) return false; // Palabras muy cortas
  if (palabra.startsWith('#')) return false; // Comentarios
  if (RegExp(r'^\d+$').hasMatch(palabra)) return false; // Solo números
  if (RegExp(r'^[^a-záéíóúüñç]+$').hasMatch(palabra)) {
    return false; // Sin letras válidas
  }

  return true;
}
