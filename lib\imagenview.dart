import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class ImagenView extends StatefulWidget {
  final String imageName;
  final int leccionId;

  const ImagenView({
    super.key,
    required this.imageName,
    required this.leccionId,
  });

  @override
  ImagenViewState createState() => ImagenViewState();
}

class ImagenViewState extends State<ImagenView> {
  String? _imagePath;
  List<MarkerPoint> markers = [];
  String imageDescription = '';
  final PhotoViewController photoViewController = PhotoViewController();
  double imageWidth = 0;
  double imageHeight = 0;
  bool isLoading = true;
  bool showTooltips = false; // Controlar la visibilidad de los tooltips

  @override
  void initState() {
    super.initState();
    _loadImagePath();
  }

  @override
  void dispose() {
    photoViewController.dispose();
    super.dispose();
  }

  Future<void> _loadImagePath() async {
    Directory directory;
    if (Platform.isAndroid) {
      directory = await getApplicationDocumentsDirectory();
      _imagePath = path.join(directory.path, 'images', widget.imageName);
    } else if (Platform.isWindows) {
      directory = Directory(
          'C:/Users/<USER>/AppData/Roaming/com.example/medicappp/images');
      _imagePath = path.join(directory.path, widget.imageName);
    } else {
      directory = await getApplicationDocumentsDirectory();
      _imagePath =
          path.join(directory.path, 'assets', 'images', widget.imageName);
    }
    debugPrint('Image path: $_imagePath');
    await _loadImageData();
    setState(() {
      isLoading = false;
    });
  }

  Future<void> _loadImageData() async {
    try {
      // Buscar imagen_id en la tabla imagenes
      final imageResponse = await Supabase.instance.client
          .from('imagenes')
          .select('imagen_id')
          .eq('imagen_nombre', widget.imageName)
          .maybeSingle();

      if (imageResponse == null) {
        debugPrint('Imagen no encontrada: ${widget.imageName}');
        return;
      }

      final imageId = imageResponse['imagen_id'];
      debugPrint('Image ID: $imageId');

      // Buscar datos en la tabla leccion_imagenes
      final response = await Supabase.instance.client
          .from('leccion_imagenes')
          .select('descripcion_especifica, markers_data')
          .eq('imagen_id', imageId)
          .eq('leccion_id', widget.leccionId)
          .maybeSingle();

      if (response == null) {
        debugPrint('No hay datos de imagen para esta lección');
        return;
      }

      final List<dynamic> markersData = response['markers_data'] ?? [];
      debugPrint('Markers data: $markersData');

      // Obtener las dimensiones de la imagen desde el JSONB
      if (markersData.isNotEmpty) {
        final firstMarker = markersData[0];
        imageWidth = firstMarker['image_width'];
        imageHeight = firstMarker['image_height'];
      }

      setState(() {
        markers = markersData.map<MarkerPoint>((data) {
          final marker = MarkerPoint(
            position: Offset(data['x_position'], data['y_position']),
            description: data['description'],
            size: data['size'].toDouble(),
            color: Color(data['color']),
            shape: MarkerShape.values[data['shape']],
          );
          debugPrint('Marker position: ${marker.position}');
          return marker;
        }).toList();
        imageDescription = response['descripcion_especifica'] ?? '';
      });
    } catch (e) {
      debugPrint('Error loading image data: $e');
    }
  }

  void _toggleTooltips() {
    setState(() {
      showTooltips = !showTooltips;
    });
  }

  void _zoomIn() {
    photoViewController.scale = photoViewController.scale! * 1.2;
  }

  void _zoomOut() {
    photoViewController.scale = photoViewController.scale! / 1.2;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromARGB(255, 103, 197, 231),
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: const Text(
          'Volver',
          style: TextStyle(color: Colors.white),
        ),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : _imagePath == null
              ? const Center(child: Text('No se pudo cargar la imagen'))
              : Column(
                  children: [
                    Expanded(
                      flex: 8,
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          debugPrint(
                              'Stack size: ${constraints.maxWidth} x ${constraints.maxHeight}');
                          final scaleX = constraints.maxWidth / imageWidth;
                          final scaleY = constraints.maxHeight / imageHeight;
                          final scale = scaleX < scaleY ? scaleX : scaleY;
                          final scaledWidth = imageWidth * scale;
                          final scaledHeight = imageHeight * scale;
                          debugPrint(
                              'Image size: $scaledWidth x $scaledHeight');
                          return Center(
                            child: Stack(
                              children: [
                                PhotoView.customChild(
                                  controller: photoViewController,
                                  backgroundDecoration: BoxDecoration(
                                    color: Colors.transparent,
                                  ),
                                  child: SizedBox(
                                    width: scaledWidth,
                                    height: scaledHeight,
                                    child: Stack(
                                      children: [
                                        Image.file(
                                          File(_imagePath!),
                                          width: scaledWidth,
                                          height: scaledHeight,
                                          fit: BoxFit.contain,
                                        ),
                                        ...markers.map((marker) {
                                          final scaledDx =
                                              marker.position.dx * scale;
                                          final scaledDy =
                                              marker.position.dy * scale;
                                          debugPrint(
                                              'Placing marker at: Offset($scaledDx, $scaledDy)');
                                          return Positioned(
                                            left: scaledDx,
                                            top: scaledDy,
                                            child: Stack(
                                              children: [
                                                Container(
                                                  width: marker.size *
                                                      scale *
                                                      1.8, // Tamaño del contenedor
                                                  height: marker.size *
                                                      scale *
                                                      1.8, // Tamaño del contenedor
                                                  decoration: BoxDecoration(
                                                    color: marker.color,
                                                    shape: marker.shape ==
                                                            MarkerShape.circle
                                                        ? BoxShape.circle
                                                        : BoxShape.rectangle,
                                                    border: Border.all(
                                                        color: Colors.white,
                                                        width: 2),
                                                  ),
                                                  child: Center(
                                                    child: Text(
                                                      '${markers.indexOf(marker) + 1}',
                                                      style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: marker.size *
                                                            0.8 *
                                                            scale *
                                                            (photoViewController
                                                                    .scale ??
                                                                1.0), // Ajustar el tamaño del texto
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        }),
                                        if (showTooltips)
                                          ...markers.map((marker) {
                                            final scaledDx =
                                                marker.position.dx * scale;
                                            final scaledDy =
                                                marker.position.dy * scale;
                                            return Positioned(
                                              left: scaledDx,
                                              top: scaledDy -
                                                  30, // Ajustar la posición del tooltip
                                              child: Container(
                                                padding: EdgeInsets.all(4),
                                                margin:
                                                    EdgeInsets.only(bottom: 4),
                                                decoration: BoxDecoration(
                                                  color: Colors.black,
                                                  borderRadius:
                                                      BorderRadius.circular(4),
                                                ),
                                                child: Text(
                                                  'Punto ${markers.indexOf(marker) + 1}: ${marker.description}',
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ),
                                            );
                                          }),
                                      ],
                                    ),
                                  ),
                                ),
                                Positioned(
                                  bottom: 20,
                                  right: 20,
                                  child: Column(
                                    children: [
                                      FloatingActionButton(
                                        onPressed: _zoomIn,
                                        child: Icon(Icons.zoom_in),
                                      ),
                                      SizedBox(height: 10),
                                      FloatingActionButton(
                                        onPressed: _zoomOut,
                                        child: Icon(Icons.zoom_out),
                                      ),
                                      SizedBox(height: 10),
                                      FloatingActionButton(
                                        onPressed: _toggleTooltips,
                                        child: Icon(showTooltips
                                            ? Icons.visibility_off
                                            : Icons.visibility),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: SingleChildScrollView(
                        child: Container(
                          padding: const EdgeInsets.all(16.0),
                          color: Colors.white,
                          child: Text(
                            imageDescription,
                            style: const TextStyle(
                                fontSize: 16.0, color: Colors.black),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
    );
  }
}

class MarkerPoint {
  final Offset position;
  final String description;
  final double size;
  final Color color;
  final MarkerShape shape;

  MarkerPoint({
    required this.position,
    required this.description,
    required this.size,
    required this.color,
    required this.shape,
  });
}

enum MarkerShape { circle, square }
