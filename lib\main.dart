import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart' as dotenv;
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'homepage.dart';
import 'package:supabase_auth_ui/supabase_auth_ui.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'new_user.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (!kIsWeb) {
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }
  }

  await dotenv.dotenv.load(fileName: "supabasekeys.env");

  await Supabase.initialize(
    url: dotenv.dotenv.env['SUPABASE_URL']!,
    anonKey: dotenv.dotenv.env['SUPABASE_ANON_KEY']!,
  );

  bool permissionGranted = await requestStoragePermission();
  if (!permissionGranted) {
    runApp(const PermissionDeniedApp());
    return;
  }

  runApp(const MyApp());
}

Future<bool> requestStoragePermission() async {
  var statusImages = await Permission.photos.status;
  var statusVideos = await Permission.videos.status;
  var statusAudio = await Permission.audio.status;

  if (statusImages.isDenied || statusImages.isPermanentlyDenied) {
    statusImages = await Permission.photos.request();
  }
  if (statusVideos.isDenied || statusVideos.isPermanentlyDenied) {
    statusVideos = await Permission.videos.request();
  }
  if (statusAudio.isDenied || statusAudio.isPermanentlyDenied) {
    statusAudio = await Permission.audio.request();
  }

  if (statusImages.isGranted &&
      statusVideos.isGranted &&
      statusAudio.isGranted) {
    return true;
  } else {
    if (statusImages.isDenied ||
        statusVideos.isDenied ||
        statusAudio.isDenied) {
      // Permission is denied by the user.
    } else if (statusImages.isPermanentlyDenied ||
        statusVideos.isPermanentlyDenied ||
        statusAudio.isPermanentlyDenied) {
      // Permission is permanently denied. The user must enable it from the app settings.
      openAppSettings();
    } else if (statusImages.isRestricted ||
        statusVideos.isRestricted ||
        statusAudio.isRestricted) {
      // Permission is restricted and cannot be requested.
    } else if (statusImages.isLimited ||
        statusVideos.isLimited ||
        statusAudio.isLimited) {
      // Permission is limited.
    }
    return false;
  }
}

class PermissionDeniedApp extends StatelessWidget {
  const PermissionDeniedApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Permission Denied'),
        ),
        body: Center(
          child: Text(
            'Storage permission not granted. Please enable it in the app settings.',
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Your App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const AuthWrapper(),
    );
  }
}

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<AuthState>(
      stream: Supabase.instance.client.auth.onAuthStateChange,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.active) {
          if (snapshot.hasData && snapshot.data!.session != null) {
            return const HomePage();
          } else {
            return const LoginPage();
          }
        } else {
          return Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }
      },
    );
  }
}

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  Future<void> _handleSignIn(String email, String password) async {
    await Supabase.instance.client.auth.signInWithPassword(
      email: email.trim(),
      password: password,
    );
  }

  @override
  Widget build(BuildContext context) {
    final emailController = TextEditingController();
    final passwordController = TextEditingController();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Login'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextField(
              controller: emailController,
              decoration: const InputDecoration(labelText: 'Email'),
            ),
            TextField(
              controller: passwordController,
              decoration: const InputDecoration(labelText: 'Password'),
              obscureText: true,
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => _handleSignIn(
                emailController.text,
                passwordController.text,
              ),
              child: const Text('Login'),
            ),
            TextButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const NewUserPage()),
                );
              },
              child: const Text('Need an account? Sign Up'),
            ),
          ],
        ),
      ),
    );
  }
}
