# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\FlutterProjects\\appmanager" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Flutter"
  "PROJECT_DIR=C:\\FlutterProjects\\appmanager"
  "FLUTTER_ROOT=C:\\Flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\FlutterProjects\\appmanager\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\FlutterProjects\\appmanager"
  "FLUTTER_TARGET=C:\\FlutterProjects\\appmanager\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\FlutterProjects\\appmanager\\.dart_tool\\package_config.json"
)
