import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import 'leccion_page.dart';
import 'leccion_edit.dart';
import 'views_chart.dart';

class TodasLasLeccionesPage extends StatefulWidget {
  const TodasLasLeccionesPage({super.key});

  @override
  State<TodasLasLeccionesPage> createState() => _TodasLasLeccionesPageState();
}

class _TodasLasLeccionesPageState extends State<TodasLasLeccionesPage> {
  final Logger _logger = Logger();
  final TextEditingController _searchController = TextEditingController();
  final ValueNotifier<List<Map<String, dynamic>>> _leccionesNotifier =
      ValueNotifier<List<Map<String, dynamic>>>([]);

  // Variables para filtros
  Set<int> _leccionesConBorrador = {};
  String _filtroTexto = '';
  String _filtroBorrador = 'todos'; // 'todos', 'con_borrador', 'sin_borrador'
  String _ordenarPor = 'nombre'; // 'nombre', 'fecha', 'visualizaciones'
  bool _ordenAscendente = true;
  bool _cargando = false;

  @override
  void initState() {
    super.initState();
    _cargarTodasLasLecciones();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _leccionesNotifier.dispose();
    super.dispose();
  }

  // Cargar todas las lecciones desde Supabase
  Future<void> _cargarTodasLasLecciones() async {
    setState(() {
      _cargando = true;
    });

    try {
      _logger.i('🔄 Cargando todas las lecciones desde Supabase');

      // Cargar lecciones con información de unidad y materia
      final response = await Supabase.instance.client
          .from('lecciones')
          .select(
              'leccion_id, leccion_nombre, leccion_contenido, unidades_id, leccion_id2, seccion, edited_at, created_at, unidades!inner(materia_id)')
          .order('leccion_nombre', ascending: true);

      // Cargar conteo de impresiones
      final impressionsResponse =
          await Supabase.instance.client.rpc('contar_impresiones_por_leccion');

      final Map<String, int> impressionsMap = {};
      for (var item in impressionsResponse) {
        final leccionId = item['leccion_id'].toString();
        final count = int.tryParse(item['total_impresiones'].toString()) ?? 0;
        impressionsMap[leccionId] = count;
      }

      // Combinar datos
      final List<Map<String, dynamic>> leccionesConConteo = [];
      for (var leccion in response) {
        final leccionIdStr = leccion['leccion_id'].toString();
        final count = impressionsMap[leccionIdStr] ?? 0;
        leccionesConConteo.add({
          ...leccion,
          'impressions_count': count,
        });
      }

      _leccionesNotifier.value = leccionesConConteo;
      _logger.i('✅ Cargadas ${leccionesConConteo.length} lecciones');

      // Cargar información de borradores
      await _cargarLeccionesConBorrador();
    } catch (e) {
      _logger.e('Error cargando lecciones: $e');
    } finally {
      setState(() {
        _cargando = false;
      });
    }
  }

  // Cargar lecciones que tienen borrador activo
  Future<void> _cargarLeccionesConBorrador() async {
    try {
      final response = await Supabase.instance.client
          .from('lecciones_borradores')
          .select('leccion_id');

      final Set<int> leccionesConBorrador = {};
      for (var item in response) {
        final leccionId = item['leccion_id'] as int?;
        if (leccionId != null) {
          leccionesConBorrador.add(leccionId);
        }
      }

      setState(() {
        _leccionesConBorrador = leccionesConBorrador;
      });

      _logger.i(
          '📝 Encontradas ${leccionesConBorrador.length} lecciones con borrador activo');
    } catch (e) {
      _logger.e('Error cargando lecciones con borrador: $e');
    }
  }

  // Filtrar y ordenar lecciones
  List<Map<String, dynamic>> _filtrarLecciones(
      List<Map<String, dynamic>> lecciones) {
    var leccionesFiltradas = lecciones.where((leccion) {
      // Filtro por texto
      final cumpleTexto = _filtroTexto.isEmpty ||
          leccion['leccion_nombre']
              .toString()
              .toLowerCase()
              .contains(_filtroTexto.toLowerCase());

      // Filtro por borrador
      final tieneBorrador =
          _leccionesConBorrador.contains(leccion['leccion_id']);
      final cumpleBorrador = _filtroBorrador == 'todos' ||
          (_filtroBorrador == 'con_borrador' && tieneBorrador) ||
          (_filtroBorrador == 'sin_borrador' && !tieneBorrador);

      return cumpleTexto && cumpleBorrador;
    }).toList();

    // Ordenar
    leccionesFiltradas.sort((a, b) {
      int comparacion = 0;

      switch (_ordenarPor) {
        case 'nombre':
          comparacion = a['leccion_nombre']
              .toString()
              .compareTo(b['leccion_nombre'].toString());
          break;
        case 'fecha':
          final fechaA =
              DateTime.tryParse(a['edited_at'] ?? a['created_at'] ?? '') ??
                  DateTime(1970);
          final fechaB =
              DateTime.tryParse(b['edited_at'] ?? b['created_at'] ?? '') ??
                  DateTime(1970);
          comparacion = fechaA.compareTo(fechaB);
          break;
        case 'visualizaciones':
          comparacion = (a['impressions_count'] ?? 0)
              .compareTo(b['impressions_count'] ?? 0);
          break;
      }

      return _ordenAscendente ? comparacion : -comparacion;
    });

    return leccionesFiltradas;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Todas las Lecciones'),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            tooltip: 'Nueva lección',
            onPressed: () async {
              await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const LeccionEditPage(leccionId: null),
                ),
              );
              // Actualizar la lista después de crear una nueva lección
              _cargarTodasLasLecciones();
            },
          ),
          IconButton(
            icon: Icon(Icons.refresh),
            tooltip: 'Actualizar',
            onPressed: _cargarTodasLasLecciones,
          ),
        ],
      ),
      body: Column(
        children: [
          // Barra de búsqueda y filtros
          _buildBarraFiltros(),
          // Lista de lecciones
          Expanded(
            child: _cargando
                ? Center(child: CircularProgressIndicator())
                : _buildListaLecciones(),
          ),
        ],
      ),
    );
  }

  // Widget para la barra de filtros
  Widget _buildBarraFiltros() {
    return Container(
      color: Colors.grey[100],
      padding: EdgeInsets.all(12),
      child: Column(
        children: [
          // Campo de búsqueda
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Buscar por nombre...',
                border: InputBorder.none,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                prefixIcon: Icon(Icons.search, color: Colors.grey[600]),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear, color: Colors.grey[600]),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _filtroTexto = '';
                          });
                        },
                      )
                    : null,
              ),
              onChanged: (value) {
                setState(() {
                  _filtroTexto = value;
                });
              },
            ),
          ),
          SizedBox(height: 12),
          // Filtros y ordenamiento
          Row(
            children: [
              // Filtro por borrador
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _filtroBorrador,
                  decoration: InputDecoration(
                    labelText: 'Estado Borrador',
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: [
                    DropdownMenuItem(value: 'todos', child: Text('Todos')),
                    DropdownMenuItem(
                        value: 'con_borrador', child: Text('Con borrador')),
                    DropdownMenuItem(
                        value: 'sin_borrador', child: Text('Sin borrador')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _filtroBorrador = value!;
                    });
                  },
                ),
              ),
              SizedBox(width: 12),
              // Ordenar por
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _ordenarPor,
                  decoration: InputDecoration(
                    labelText: 'Ordenar por',
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8)),
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: [
                    DropdownMenuItem(value: 'nombre', child: Text('Nombre')),
                    DropdownMenuItem(value: 'fecha', child: Text('Fecha')),
                    DropdownMenuItem(
                        value: 'visualizaciones',
                        child: Text('Visualizaciones')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _ordenarPor = value!;
                    });
                  },
                ),
              ),
              SizedBox(width: 8),
              // Botón orden ascendente/descendente
              IconButton(
                icon: Icon(_ordenAscendente
                    ? Icons.arrow_upward
                    : Icons.arrow_downward),
                tooltip: _ordenAscendente ? 'Ascendente' : 'Descendente',
                onPressed: () {
                  setState(() {
                    _ordenAscendente = !_ordenAscendente;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Widget para la lista de lecciones
  Widget _buildListaLecciones() {
    return ValueListenableBuilder<List<Map<String, dynamic>>>(
      valueListenable: _leccionesNotifier,
      builder: (context, todasLecciones, child) {
        final leccionesFiltradas = _filtrarLecciones(todasLecciones);

        if (leccionesFiltradas.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
                SizedBox(height: 16),
                Text(
                  'No se encontraron lecciones',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
                SizedBox(height: 8),
                Text(
                  'Intenta ajustar los filtros',
                  style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: leccionesFiltradas.length,
          itemBuilder: (context, index) {
            final leccion = leccionesFiltradas[index];
            return _buildLeccionCard(leccion);
          },
        );
      },
    );
  }

  // Widget para cada tarjeta de lección
  Widget _buildLeccionCard(Map<String, dynamic> leccion) {
    final tieneBorrador = _leccionesConBorrador.contains(leccion['leccion_id']);

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: tieneBorrador ? Colors.orange : Colors.blue,
          child: Icon(
            tieneBorrador ? Icons.edit : Icons.book,
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                leccion['leccion_nombre'],
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
            if (tieneBorrador)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'BORRADOR',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 4),
            Text(
              '${leccion['impressions_count'] ?? 0} visualizaciones • ID: ${leccion['leccion_id']} • Unidad: ${leccion['unidades_id']}',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
            if (leccion['edited_at'] != null)
              Text(
                'Editado: ${_formatearFecha(leccion['edited_at'])}',
                style: TextStyle(fontSize: 11, color: Colors.grey[500]),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Botón Ver lección
            IconButton(
              icon: Icon(Icons.visibility, color: Colors.blue, size: 20),
              tooltip: 'Ver lección',
              onPressed: () async {
                // Navegar a ver lección y esperar el resultado
                await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => LeccionPage(
                      titulo: leccion['leccion_nombre'],
                      contenido: leccion['leccion_contenido'],
                      leccionId: leccion['leccion_id'],
                    ),
                  ),
                );

                // Actualizar datos al regresar (por si editaron desde LeccionPage)
                await _cargarLeccionesConBorrador();
              },
            ),
            // Botón Borrar Borrador (solo si tiene borrador)
            if (tieneBorrador)
              IconButton(
                icon: Icon(Icons.delete, color: Colors.red, size: 20),
                tooltip: 'Borrar Borrador',
                onPressed: () => _eliminarBorrador(leccion),
              ),
            // Botón Editar
            IconButton(
              icon: Icon(Icons.edit, color: Colors.green, size: 20),
              tooltip: 'Editar lección',
              onPressed: () async {
                // Navegar a editar y esperar el resultado
                await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => LeccionEditPage(
                      leccionContenido: leccion['leccion_contenido'],
                      leccionTitulo: leccion['leccion_nombre'],
                      leccionId: leccion['leccion_id'],
                      unidadesId: leccion['unidades_id'],
                      materiaId: leccion['unidades']
                          ['materia_id'], // Pasar materia_id
                      categoriaId:
                          leccion['leccion_id2'], // Pasar la categoría real
                      leccionId2:
                          leccion['leccion_id2'], // Pasar la categoría real
                      seccion: leccion['seccion'],
                      editedAt: leccion['edited_at'],
                      createdAt: leccion['created_at'],
                    ),
                  ),
                );

                // Actualizar datos al regresar
                await _cargarLeccionesConBorrador();
              },
            ),
            // Botón Estadísticas
            IconButton(
              icon: Icon(Icons.bar_chart, color: Colors.purple, size: 20),
              tooltip: 'Ver estadísticas',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ViewsChartPage(
                      leccionId: leccion['leccion_id'],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        onTap: () async {
          // Navegar a ver lección y esperar el resultado
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => LeccionPage(
                titulo: leccion['leccion_nombre'],
                contenido: leccion['leccion_contenido'],
                leccionId: leccion['leccion_id'],
              ),
            ),
          );

          // Actualizar datos al regresar (por si editaron desde LeccionPage)
          await _cargarLeccionesConBorrador();
        },
      ),
    );
  }

  // Formatear fecha para mostrar
  String _formatearFecha(String fechaString) {
    try {
      final fecha = DateTime.parse(fechaString);
      return '${fecha.day}/${fecha.month}/${fecha.year} ${fecha.hour}:${fecha.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'Fecha inválida';
    }
  }

  // Eliminar borrador de una lección específica
  Future<void> _eliminarBorrador(Map<String, dynamic> leccion) async {
    final leccionId = leccion['leccion_id'];
    final leccionNombre = leccion['leccion_nombre'] ?? 'Sin nombre';

    // Mostrar diálogo de confirmación
    final bool? confirmar = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange),
              SizedBox(width: 8),
              Text('Eliminar Borrador'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('¿Estás seguro de que quieres eliminar el borrador de:'),
              SizedBox(height: 8),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '"$leccionNombre"',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              SizedBox(height: 12),
              Text(
                'Esta acción no se puede deshacer. El borrador se eliminará permanentemente.',
                style: TextStyle(color: Colors.red, fontSize: 12),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text('Eliminar Borrador'),
            ),
          ],
        );
      },
    );

    if (confirmar == true) {
      try {
        // Eliminar el borrador de la base de datos
        await Supabase.instance.client
            .from('lecciones_borradores')
            .delete()
            .eq('leccion_id', leccionId);

        // Actualizar la lista local
        setState(() {
          _leccionesConBorrador.remove(leccionId);
        });

        // Mostrar mensaje de éxito
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text('Borrador eliminado: "$leccionNombre"'),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }

        _logger.i(
            '🗑️ Borrador eliminado para lección: $leccionNombre (ID: $leccionId)');
      } catch (e) {
        _logger.e('Error eliminando borrador: $e');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.error, color: Colors.white),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text('Error al eliminar borrador: $e'),
                  ),
                ],
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 5),
            ),
          );
        }
      }
    }
  }
}
