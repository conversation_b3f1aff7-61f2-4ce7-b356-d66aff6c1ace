import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'leccion_edit.dart';
import 'leccionmanager.dart';
import 'datamanagement.dart';
import 'nuevadefinicion.dart';
import 'listadefiniciones.dart';
import 'listausuarios.dart';
import 'usuario_edit.dart';
import 'notificationmanager.dart';
import 'homepage_componentes/drawer_homepage.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const HomePage(),
      routes: {
        '/HomePage': (context) => const HomePage(),
        '/nuevaLeccion': (context) => const LeccionEditPage(leccionId: null),
        '/editarLecciones': (context) => const LeccionManagerPage(),
        '/editar': (context) => const LeccionManagerPage(),
        '/Datamanagement': (context) => const Datamanagement(),
        '/nuevaDefinicion': (context) => const NuevaDefinicion(),
        '/listaDefiniciones': (context) => const ListaDefiniciones(),
        '/usuarios': (context) => const ListaUsuariosPage(),
        '/usuario_edit': (context) => const UsuarioEditPage(),
        '/notificationManager': (context) => const NotificationManager(),
      },
    );
  }
}

class _HomePageNavigatorObserver extends NavigatorObserver {
  final Function() onPop;

  _HomePageNavigatorObserver({required this.onPop});

  @override
  void didPop(Route route, Route? previousRoute) {
    super.didPop(route, previousRoute);
    onPop();
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final int _selectedIndex = 0;
  late _HomePageNavigatorObserver _navigatorObserver;

  @override
  void initState() {
    super.initState();
    _navigatorObserver = _HomePageNavigatorObserver(onPop: () {});
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations(DeviceOrientation.values);
    super.dispose();
  }

  void _logout() async {
    await Supabase.instance.client.auth.signOut();
  }

  @override
  Widget build(BuildContext context) {
    const routes = [
      '/miCuenta',
      '/editarLecciones',
      '/notificationManager',
      '/nuevaDefinicion',
      '/listaDefiniciones',
      '/usuarios',
      '/Datamanagement',
    ];
    const titles = [
      'Mi cuenta',
      'Editar Lecciones',
      'Gestionar Notificaciones',
      'Nueva Definición',
      'Lista Definiciones',
      'Usuarios',
      'Data management',
    ];
    const icons = [
      Icons.account_circle,
      Icons.edit,
      Icons.notifications,
      Icons.photo_library,
      Icons.description,
      Icons.list_alt,
      Icons.people,
      Icons.storage,
    ];

    const categories = {
      'Perfil': [0],
      'Contenido': [1, 3, 4],
      'Administración': [2, 5, 6],
    };

    final pages = [
      Scaffold(
        appBar: AppBar(
          title: const Text('Panel de Control'),
          backgroundColor: Colors.blue[700],
          elevation: 0,
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.logout),
              onPressed: _logout,
            ),
          ],
        ),
        drawer: DrawerHomePage(client: Supabase.instance.client),
        backgroundColor: Colors.grey[50],
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue[700],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.dashboard, color: Colors.white, size: 32),
                    SizedBox(width: 16),
                    Text(
                      'Bienvenido al Panel de Control',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              Expanded(
                child: ListView.builder(
                  itemCount: categories.length,
                  itemBuilder: (context, categoryIndex) {
                    final categoryName =
                        categories.keys.elementAt(categoryIndex);
                    final categoryItems =
                        categories.values.elementAt(categoryIndex);

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(
                            categoryName,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ),
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 6,
                            childAspectRatio: 1.3,
                            mainAxisSpacing: 3,
                            crossAxisSpacing: 3,
                          ),
                          itemCount: categoryItems.length,
                          itemBuilder: (context, index) {
                            final itemIndex = categoryItems[index];
                            return Card(
                              elevation: 2,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: InkWell(
                                borderRadius: BorderRadius.circular(8),
                                onTap: () {
                                  Navigator.pushNamed(
                                      context, routes[itemIndex]);
                                },
                                child: Padding(
                                  padding: const EdgeInsets.all(1.0),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        width: 32,
                                        height: 32,
                                        decoration: BoxDecoration(
                                          color: Colors.blue[700],
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Icon(icons[itemIndex],
                                            size: 18, color: Colors.blue[700]),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        titles[itemIndex],
                                        textAlign: TextAlign.center,
                                        style: const TextStyle(
                                          fontSize: 9,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 16),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    ];

    return MaterialApp(
      navigatorObservers: [_navigatorObserver],
      routes: {
        '/HomePage': (context) => const HomePage(),
        '/nuevaLeccion': (context) => const LeccionEditPage(leccionId: null),
        '/editarLecciones': (context) => const LeccionManagerPage(),
        '/editar': (context) => const LeccionManagerPage(),
        '/Datamanagement': (context) => const Datamanagement(),
        '/nuevaDefinicion': (context) => const NuevaDefinicion(),
        '/listaDefiniciones': (context) => const ListaDefiniciones(),
        '/usuarios': (context) => const ListaUsuariosPage(),
        '/usuario_edit': (context) => const UsuarioEditPage(),
        '/notificationManager': (context) => const NotificationManager(),
      },
      home: Scaffold(
        body: IndexedStack(
          index: _selectedIndex,
          children: pages,
        ),
      ),
    );
  }
}
